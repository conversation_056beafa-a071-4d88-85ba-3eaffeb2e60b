stages:
  - preparation
  - testing
  - environment
  - building
  - pushing
  - deploy
  - onSystem
  - onSystem-cleanup
  - manual
  - sbom

# Variables
variables:
  TESTINGBRANCH: develop
  STAGINGBRANCH: staging
  PRODUCTIONBRANCH: main
  BASEIMAGE: gitlab-docker.mogic.com/docker/mogic-base-image:webapp-noble-php8.3

.add_deploy_key: &add_deploy_key
  - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client git -y )'
  - eval $(ssh-agent -s)
  - DEPLOY_KEY=$(vault kv get -field=ssh-key-private devops/SSH/Mogic-General-Refresh)
  - echo "$DEPLOY_KEY" | tr -d '\r' | ssh-add - > /dev/null
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - ssh-keyscan gitlab.mogic.com >> ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - if [ "$CI_COMMIT_REF_SLUG" == "$PRODUCTIONBRANCH" ];
      then export ENVIRONMENT='production' export APP_ENVIRONMENT=production; export VAULTPATH_KV2="projects_prod/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME"; export VAULTPATH_KV1="projects_prod/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME";
      elif [ "$CI_COMMIT_REF_SLUG" == "$STAGINGBRANCH" ]; then export ENVIRONMENT='staging'; export APP_ENVIRONMENT=staging; export VAULTPATH_KV2="projects/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/staging"; export VAULTPATH_KV1="projects/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/staging";
      else export ENVIRONMENT='testing'; export APP_ENVIRONMENT=development; export VAULTPATH_KV2="projects/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/testing"; VAULTPATH_KV1="projects/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/testing"; fi

composer:
  stage: preparation
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - cd typo3
    - rm -rf vendor index.php typo3 _assets  || true
    - composer2 install --prefer-dist --no-ansi --no-interaction --no-progress --no-scripts
  cache:
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/vendor/
        - ./typo3/public/
        - ./typo3/public/_assets/
      policy: pull-push
  only:
    refs:
      - branches
    changes:
      - typo3/composer.json
      - typo3/composer.lock
      - .gitlab-ci.yml

phive:
  stage: preparation
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - cd typo3 && phive install --copy --temporary --trust-gpg-keys 0xE82B2FB314E9906E
  cache:
    - key: phive-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/tools/
      policy: pull-push
  only:
    refs:
      - branches
  except:
    - schedules

test-php:
  stage: testing
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - cd typo3/
    - ./tools/php-cs-fixer check --diff --show-progress=none
    - cd packages/gdmcom && make local-checkstyle
  cache:
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/vendor/
      policy: pull
    - key: phive-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/tools/
      policy: pull
    - key: checkstyle-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/.php-cs-fixer.cache
      policy: pull-push
  dependencies:
    - phive
  only:
    - branches
  except:
    - schedules

build-assets:
  stage: building
  image: gitlab-docker.mogic.com/gdmcom/typo3/build:latest
  script:
    - source ~/.profile;
    - cd /builds/gdmcom/typo3/typo3/packages/gdmcom/ && nvm install && yarn install && yarn prod
    - cd /builds/gdmcom/typo3/typo3/packages/giby/ && nvm install && yarn install && yarn prod
  artifacts:
    paths:
      - ./typo3/packages/gdmcom/node_modules/
      - ./typo3/packages/gdmcom/Resources/Public/
      - ./typo3/packages/giby/node_modules/
      - ./typo3/packages/giby/Resources/Public/
      - ./typo3/public/build/
    expire_in: 1 days
    when: always
  cache:
    - key: npm-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/packages/gdmcom/node_modules/
        - ./typo3/packages/giby/node_modules/
      policy: pull-push
    - key: frontend-assets-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/public/build/
      policy: push
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/vendor/
      policy: pull
  #review apps
  only:
    refs:
      - branches
  # only:
  #   variables:
  #     - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
  #     - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

.push-script: &push-script
  - export GIT_TAG_SENTRY=$(git describe --tags --always)
  # Login to Docker-Registry
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com
  - gitlabci-templating -inputTemplate ./template-data/ci/site-gdmcom-template -vaultPath $VAULTPATH_KV2 -outputFile ./docker/typo3/conf/nginx/site-gdmcom
  # Build and Push Dockerimage
  - docker pull $BASEIMAGE
  - docker build --build-arg BASEIMAGE=$BASEIMAGE -f docker/typo3/Dockerfile -t gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG} .
  - docker push gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG}
  # Replace Variables in docker-compose-file (environment-related)
  - export DOCKERIMAGE=gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG}
  - gitlabci-templating -inputTemplate ./template-data/ci/docker-compose-template-$ENVIRONMENT -vaultPath $VAULTPATH_KV2 -outputFile $CI_PROJECT_DIR/docker-compose.yml

.deploy_script: &deploy_script
  - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
  - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
  - ssh root@$TARGET_HOST "mkdir -p /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
  - rsync -havz --delete -e 'ssh -p 22' docker-compose.yml "root@$TARGET_HOST:/opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
  - ssh root@$TARGET_HOST "docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com"
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose pull"
  - INSTANCES=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG") || true
  - if [[ -z $INSTANCES ]]; then ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose up -d --force-recreate"; exit 0; fi;
  - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
  - OLD_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created < .[1].Created then .[0].Id else if .[1].Id != "null" then .[1].Id else .[0].Id end end'")
  - if [[ $NEW_INSTANCE_ID != $OLD_INSTANCE_ID ]]; then ssh root@$TARGET_HOST "docker stop $OLD_INSTANCE_ID && docker rm $OLD_INSTANCE_ID"; fi
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose up -d --no-recreate --scale typo3-$CI_COMMIT_REF_SLUG=2";


## review apps
# buildAndPush:
#   stage: pushing
#   image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
#   before_script: *add_deploy_key
#   variables:
#     DOCKER_DRIVER: overlay2
#   script: *push-script
#   artifacts:
#     paths:
#       - docker-compose.yml
#     expire_in: 1 days
#     when: always
#   cache:
#     # - key: npm-$CI_COMMIT_REF_SLUG
#     #   paths:
#     #     - ./typo3/packages/gdmcom/node_modules/
#     #   policy: pull
#     - key: composer-$CI_COMMIT_REF_SLUG
#       paths:
#         - ./typo3/vendor/
#       policy: pull
#     - key: frontend-assets-$CI_COMMIT_REF_SLUG
#       paths:
#         - ./typo3/public/build/
#       policy: pull
# only:
#   refs:
#     - branches
# except:
#   variables:
#     - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
#     - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

buildAndPushTesting:
  stage: pushing
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    DOCKER_DRIVER: overlay2
    TYPO3_CONTEXT: Development/Testing
  script: *push-script
  artifacts:
    paths:
      - docker-compose.yml
    expire_in: 1 days
    when: always
  dependencies:
    - composer
  cache:
    # - key: npm-$CI_COMMIT_REF_SLUG
    #   paths:
    #     - ./typo3/packages/gdmcom/node_modules/
    #   policy: pull
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/vendor/
      policy: pull
    - key: frontend-assets-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/public/build/
      policy: pull
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

buildAndPushStaging:
  stage: pushing
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    DOCKER_DRIVER: overlay2
    TYPO3_CONTEXT: Production/Staging
  script: *push-script
  artifacts:
    paths:
      - docker-compose.yml
    expire_in: 1 days
    when: always
  dependencies:
    - composer
  cache:
    # - key: npm-$CI_COMMIT_REF_SLUG
    #   paths:
    #     - ./typo3/packages/gdmcom/node_modules/
    #   policy: pull
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/vendor/
      policy: pull
    - key: frontend-assets-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/public/build/
      policy: pull
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH

buildAndPushProduction:
  stage: pushing
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    DOCKER_DRIVER: overlay2
    TYPO3_CONTEXT: Production
  script: *push-script
  artifacts:
    paths:
      - docker-compose.yml
    expire_in: 1 days
    when: always
  dependencies:
    - composer
  cache:
    # - key: npm-$CI_COMMIT_REF_SLUG
    #   paths:
    #     - ./typo3/packages/gdmcom/node_modules/
    #   policy: pull
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/vendor/
      policy: pull
    - key: frontend-assets-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/public/build/
      policy: pull
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH

## review apps
# deploy_review:
#   stage: deploy
#   image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
#   variables:
#     DOCKER_DRIVER: overlay2
#   before_script: *add_deploy_key
#   script: *deploy_script
#   dependencies:
#     - buildAndPushTesting
#   environment:
#     name: review/$CI_COMMIT_REF_NAME
#     url: https://$CI_COMMIT_REF_SLUG.gdmcom.mogic-server.de
    # on_stop: stop_testing
  # only:
#   refs:
#     - branches
# except:
#   variables:
#     - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
#     - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

deploy_testing:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script: *add_deploy_key
  script: *deploy_script
  dependencies:
    - buildAndPushTesting
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: https://$CI_COMMIT_REF_SLUG.gdmcom.mogic-server.de
    ## review apps
    # on_stop: stop_testing
  ## review apps
  # only:
  #   - branches
  # except:
  #   variables:
  #     - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
  #     - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

deploy_staging:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script: *add_deploy_key
  script: *deploy_script
  dependencies:
    - buildAndPushStaging
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: https://$CI_COMMIT_REF_SLUG.gdmcom.mogic-server.de
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH

deploy_Production:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script: *add_deploy_key
  script: *deploy_script
  dependencies:
    - buildAndPushProduction
  environment:
    name: production
    url: https://$CI_COMMIT_REF_SLUG.gdmcom.mogic-server.de
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH

# stop_testing:
#   stage: deploy
#   image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
#   variables:
#     GIT_STRATEGY: none
#   before_script: *add_deploy_key
#   script:
#     - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
#     - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
#     # stop container, remove image, remove folder.
#     - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose down --rmi all -v --remove-orphans"
#     - ssh root@$TARGET_HOST "cd /opt/ && rm -rf $CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
#   environment:
#     name: review/$CI_COMMIT_REF_NAME
#     action: stop
#   when: manual
#   dependencies: []
#   except:
#     variables:
#       - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
#       - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

onSystem-permissions-and-db:
  stage: onSystem
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID sh -c 'chown -R www-data:www-data /var/www/'"
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 database:updateschema safe'"
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 install:fixfolderstructure'"
  ## review apps
  # only:
  #   - branches
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

onSystem-clearCaches:
  stage: onSystem
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 cache:flush'"
  ## review apps
  # only:
  #   - branches
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

onSystem-cleanupOldInstance:
  stage: onSystem-cleanup
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - OLD_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created < .[1].Created then .[0].Id else if .[1].Id != "null" then .[1].Id else .[0].Id end end'")
    - if [[ $NEW_INSTANCE_ID != $OLD_INSTANCE_ID ]]; then ssh root@$TARGET_HOST "docker stop $OLD_INSTANCE_ID && docker rm $OLD_INSTANCE_ID"; else echo 'There is just one running instance, did nothing'; fi
  ## review apps
  # only:
  #   - branches
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

overwrite-with-main-data:
  stage: manual
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    # Mysql
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - export MINIO_DUMP_USER=$(vault kv get -field="db-dumps-id" $VAULTPATH_KV1)
    - export MINIO_DUMP_PASSWORD=$(vault kv get -field="db-dumps-password" $VAULTPATH_KV1)
    - export MINIO_DUMP_DOMAIN=$(vault kv get -field="db-dumps_login_url" $VAULTPATH_KV1)
    - MARIADB_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q mariadb-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - TYPO3_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")

    - ssh root@$TARGET_HOST "mc alias set mogic-minio $MINIO_DUMP_DOMAIN $MINIO_DUMP_USER $MINIO_DUMP_PASSWORD" || true
    - mc alias set mogic-minio $MINIO_DUMP_DOMAIN $MINIO_DUMP_USER $MINIO_DUMP_PASSWORD || true
    # List all files in the minio bucket
    - files=($(mc find "mogic-minio/gdmcom-dumps/gdmcom/main/"))
    # Find the newest file
    - newest_file=${files[0]}
    - |+
      for file in "${files[@]}"; do
        if [[ "$file" > "$newest_file" ]]; then
          newest_file="$file"
        fi
      done
    - chosen_file="$newest_file"
    - ssh root@$TARGET_HOST "mc cp $chosen_file /tmp/datadump_gdmcom.sql.gz"
    - ssh root@$TARGET_HOST "docker cp /tmp/datadump_gdmcom.sql.gz $MARIADB_INSTANCE_ID:/tmp/datadump_gdmcom.sql.gz"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$CI_COMMIT_REF_SLUG sh -c 'zcat /tmp/datadump_gdmcom.sql.gz | mysql -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE"'"
    - ssh root@$TARGET_HOST "docker exec -t $TYPO3_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 database:updateschema'"
    - rsync -avz data/mariadb/local-storage.sql root@$TARGET_HOST:/tmp/local-storage.sql
    - ssh root@$TARGET_HOST "docker cp /tmp/local-storage.sql $MARIADB_INSTANCE_ID:/tmp/local-storage.sql"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$CI_COMMIT_REF_SLUG sh -c ' mysql -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE" < /tmp/local-storage.sql'"
    - ssh root@$TARGET_HOST "docker exec -t $TYPO3_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 cache:flush'"
    - ssh root@$TARGET_HOST "rm /tmp/datadump_gdmcom.sql.gz"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$CI_COMMIT_REF_SLUG sh -c 'rm /tmp/datadump_gdmcom.sql.gz'"
    - ssh root@$TARGET_HOST "mc alias remove mogic-minio" || true
  when: manual
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH

.create_data_dump: &create_data_dump
  - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
  - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
  # get db dump gzip file
  - DATE=$(date '+%Y-%m-%d_%H-%M-%S')
  - DUMP_FILE_NAME=$DATE-gdmcom.sql
  - export DATABASE_NAME=$(vault kv get -field="db_database" $VAULTPATH_KV1)
  - MARIADB_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q mariadb-$CI_COMMIT_REF_SLUG) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
  - ssh root@$TARGET_HOST "docker exec -t $MARIADB_INSTANCE_ID sh -c ' mysqldump --no-tablespaces -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE" | gzip > /tmp/$DUMP_FILE_NAME.gz'"
  - ssh root@$TARGET_HOST "docker cp $MARIADB_INSTANCE_ID:/tmp/$DUMP_FILE_NAME.gz /tmp/$DUMP_FILE_NAME.gz"
  # get minio connection parameters
  - export MINIO_WRITE_USER=$(vault kv get -field="db-dumps-id" $VAULTPATH_KV1)
  - export MINIO_WRITE_PASSWORD=$(vault kv get -field="db-dumps-password" $VAULTPATH_KV1)
  - export MINIO_DOMAIN=$(vault kv get -field="db-dumps_login_url" $VAULTPATH_KV1)
  # create minio connection
  - ssh root@$TARGET_HOST "mc alias set mogic-minio $MINIO_DOMAIN $MINIO_WRITE_USER $MINIO_WRITE_PASSWORD" || true
  # sync db dump file to minio
  - ssh root@$TARGET_HOST "mc cp /tmp/$DUMP_FILE_NAME.gz mogic-minio/gdmcom-dumps/gdmcom/$CI_COMMIT_REF_SLUG/$DUMP_FILE_NAME.gz"
  # cleanup dump files from host
  - ssh root@$TARGET_HOST "docker exec -t $MARIADB_INSTANCE_ID sh -c 'rm /tmp/$DUMP_FILE_NAME.gz'"
  - ssh root@$TARGET_HOST "rm /tmp/$DUMP_FILE_NAME.gz"
  - ssh root@$TARGET_HOST "mc alias remove mogic-minio" || true

# create-test-data_dump:
#   stage: manual
#   image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
#   before_script: *add_deploy_key
#   script: *create_data_dump
#   when: manual
#   dependencies: []
#   only:
#     variables:
#       - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH
#       - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH

create-prod-data_dump:
  stage: manual
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script: *create_data_dump
  when: manual
  dependencies: []
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH

generate-sbom-composer-typo3:
  stage: sbom
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - composer2 global config --no-plugins allow-plugins.cyclonedx/cyclonedx-php-composer true || true
    - composer2 global require cyclonedx/cyclonedx-php-composer
    - cd typo3
    - composer2 make-bom || composer2 CycloneDX:make-sbom --output-file=bom.xml
    - DEPTRACK_URL=$(vault kv get -field="deptrack_api_url" devops/Dependencytrack)
    - DEPTRACK_API_KEY=$(vault kv get -field="deptrack_api_key" devops/Dependencytrack)
    - "curl -X POST $DEPTRACK_URL -H \"Content-Type: multipart/form-data\" -H \"X-API-Key: $DEPTRACK_API_KEY\" -F \"autoCreate=true\" -F \"projectName=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-typo3-composer\" -F \"projectVersion=$CI_COMMIT_TAG\" -F bom=@bom.xml"
  dependencies: []
  only:
    - tags

generate-sbom-npm-typo3:
  stage: sbom
  image: gitlab-docker.mogic.com/gdmcom/typo3/build:latest
  script:
    - source ~/.profile;
    - cd ./typo3/packages/gdmcom/
    - nvm install && yarn install
    - yarn global add @cyclonedx/cyclonedx-npm@2.1.0
    - cyclonedx-npm --ignore-npm-errors --output-format XML --output-file bom.xml
    - DEPTRACK_URL=$(vault kv get -field="deptrack_api_url" devops/Dependencytrack)
    - DEPTRACK_API_KEY=$(vault kv get -field="deptrack_api_key" devops/Dependencytrack)
    - "curl -X POST $DEPTRACK_URL -H \"Content-Type: multipart/form-data\" -H \"X-API-Key: $DEPTRACK_API_KEY\" -F \"autoCreate=true\" -F \"projectName=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-typo3-node\" -F \"projectVersion=$CI_COMMIT_TAG\" -F bom=@bom.xml"
  dependencies: []
  only:
    - tags

generate-sbom-npm-giby:
  stage: sbom
  image: gitlab-docker.mogic.com/gdmcom/typo3/build:latest
  script:
    - source ~/.profile;
    - cd ./typo3/packages/giby/
    - nvm install && yarn install
    - yarn global add @cyclonedx/cyclonedx-npm@2.1.0
    - cyclonedx-npm --ignore-npm-errors --output-format XML --output-file bom.xml
    - DEPTRACK_URL=$(vault kv get -field="deptrack_api_url" devops/Dependencytrack)
    - DEPTRACK_API_KEY=$(vault kv get -field="deptrack_api_key" devops/Dependencytrack)
    - "curl -X POST $DEPTRACK_URL -H \"Content-Type: multipart/form-data\" -H \"X-API-Key: $DEPTRACK_API_KEY\" -F \"autoCreate=true\" -F \"projectName=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-giby-node\" -F \"projectVersion=$CI_COMMIT_TAG\" -F bom=@bom.xml"
  dependencies: []
  only:
    - tags
