---
services:

  mariadb-{{.CI_COMMIT_REF_SLUG}}:
    image: mariadb:10.5.23
    restart: always
    volumes:
      - mariadb-data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD={{.db_rootUser_password}}
      - MYSQL_HOST={{.db_host}}
      - MYSQL_DATABASE={{.db_database}}
      - MYSQL_USER={{.mariadb_id}}
      - MYSQL_PASSWORD={{.mariadb_password}}
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--su-mysql", "--connect"]
      interval: 1s
      timeout: 5s
      retries: 90

  minio-{{.CI_COMMIT_REF_SLUG}}:
    image: minio/minio
    command: server /data
    restart: always
    environment:
      - "MINIO_ROOT_USER={{.minio_root_user}}"
      - "MINIO_ROOT_PASSWORD={{.minio_root_password}}"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.minio_gdmcom_domain}}`) || Host(`{{.minio_giby_domain}}`)"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.http.middlewares.minio-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.scheme=https"
      - "traefik.http.middlewares.minio-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.permanent=true"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.middlewares=minio-{{.CI_COMMIT_REF_SLUG}}-redirect"

      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.rule=Host(`{{.minio_gdmcom_domain}}`)"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.entrypoints=websecure"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.tls=true"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.middlewares=secHeaders@file"

      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}-gibysecure.rule=Host(`{{.minio_giby_domain}}`)"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}-gibysecure.entrypoints=websecure"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}-gibysecure.tls=true"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}-gibysecure.tls.certresolver=letsencryptresolver"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}-gibysecure.middlewares=secHeaders@file"
    volumes:
      - ./data/minio:/data

  typo3-{{.CI_COMMIT_REF_SLUG}}:
    image: {{.DOCKERIMAGE}}
    restart: always
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.typo3_domain}}`) || Host(`www.{{.typo3_domain}}`) || Host(`{{.typo3_mogic_giby_domain}}`) || Host(`{{.typo3_gdmcom_domain}}`) || Host(`www.{{.typo3_gdmcom_domain}}`) || Host(`{{.typo3_giby_domain}}`) || Host(`www.{{.typo3_giby_domain}}`)"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.http.middlewares.typo3-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.scheme=https"
      - "traefik.http.middlewares.typo3-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.permanent=true"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.middlewares=typo3-{{.CI_COMMIT_REF_SLUG}}-redirect"

      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.rule=Host(`{{.typo3_domain}}`) || Host(`www.{{.typo3_domain}}`)"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.entrypoints=websecure"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.tls=true"
      - "traefik.http.middlewares.wwwToNonWww.redirectregex.regex=^https://www.{{.typo3_domain}}(.*)"
      - "traefik.http.middlewares.wwwToNonWww.redirectregex.replacement=https://{{.typo3_domain}}$${1}"
      - "traefik.http.middlewares.wwwToNonWww.redirectregex.permanent=true"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.middlewares=wwwToNonWww,secHeaders@file"

      - "traefik.http.routers.typo3-giby-{{.CI_COMMIT_REF_SLUG}}secure.rule=Host(`{{.typo3_giby_domain}}`) || Host(`www.{{.typo3_giby_domain}}`)"
      - "traefik.http.routers.typo3-giby-{{.CI_COMMIT_REF_SLUG}}secure.entrypoints=websecure"
      - "traefik.http.routers.typo3-giby-{{.CI_COMMIT_REF_SLUG}}secure.tls=true"
      - "traefik.http.middlewares.gibywwwToNonWww.redirectregex.regex=^https://www.{{.typo3_giby_domain}}(.*)"
      - "traefik.http.middlewares.gibywwwToNonWww.redirectregex.replacement=https://{{.typo3_giby_domain}}$${1}"
      - "traefik.http.middlewares.gibywwwToNonWww.redirectregex.permanent=true"
      - "traefik.http.routers.typo3-giby-{{.CI_COMMIT_REF_SLUG}}secure.middlewares=gibywwwToNonWww,secHeaders@file"

      - "traefik.http.routers.gdmcom-domain-{{.CI_COMMIT_REF_SLUG}}secure.rule=Host(`{{.typo3_gdmcom_domain}}`) || Host(`www.{{.typo3_gdmcom_domain}}`)"
      - "traefik.http.routers.gdmcom-domain-{{.CI_COMMIT_REF_SLUG}}secure.entrypoints=websecure"
      - "traefik.http.routers.gdmcom-domain-{{.CI_COMMIT_REF_SLUG}}secure.tls=true"
      - "traefik.http.routers.gdmcom-domain-{{.CI_COMMIT_REF_SLUG}}secure.tls.certresolver=letsencryptresolver"
      - "traefik.http.middlewares.gdmcomwwwRedirect.redirectregex.regex=^https://www.{{.typo3_gdmcom_domain}}(.*)"
      - "traefik.http.middlewares.gdmcomwwwRedirect.redirectregex.replacement=https://{{.typo3_domain}}/ueber-uns/gdmcom"
      - "traefik.http.middlewares.gdmcomwwwRedirect.redirectregex.permanent=true"
      - "traefik.http.middlewares.gdmcomNonwwwRedirect.redirectregex.regex=^https://{{.typo3_gdmcom_domain}}(.*)"
      - "traefik.http.middlewares.gdmcomNonwwwRedirect.redirectregex.replacement=https://{{.typo3_domain}}/ueber-uns/gdmcom"
      - "traefik.http.middlewares.gdmcomNonwwwRedirect.redirectregex.permanent=true"
      - "traefik.http.routers.gdmcom-domain-{{.CI_COMMIT_REF_SLUG}}secure.middlewares=gdmcomwwwRedirect,gdmcomNonwwwRedirect,secHeaders@file"

      - "traefik.http.routers.typo3-mogic-giby-{{.CI_COMMIT_REF_SLUG}}secure.rule=Host(`{{.typo3_mogic_giby_domain}}`)"
      - "traefik.http.routers.typo3-mogic-giby-{{.CI_COMMIT_REF_SLUG}}secure.entrypoints=websecure"
      - "traefik.http.routers.typo3-mogic-giby-{{.CI_COMMIT_REF_SLUG}}secure.tls=true"
      - "traefik.http.routers.typo3-mogic-giby-{{.CI_COMMIT_REF_SLUG}}secure.tls.certresolver=letsencryptresolver"
      - "traefik.http.routers.typo3-mogic-giby-{{.CI_COMMIT_REF_SLUG}}secure.middlewares=typo3-mogic-giby-{{.CI_COMMIT_REF_SLUG}}-auth,secHeaders@file"
      - "traefik.http.middlewares.typo3-mogic-giby-{{.CI_COMMIT_REF_SLUG}}-auth.basicauth.users={{.basic_auth}}"
    environment:
      - MINIO_LOCAL_GDMCOM_USER={{.minio_gdmcom_typo3_user}}
      - MINIO_LOCAL_GDMCOM_PASSWORD={{.minio_gdmcom_typo3_password}}
      - MINIO_LOCAL_GDMCOM_PUBLIC_URL={{.minio_gdmcom_domain}}/{{.minio_gdmcom_bucket}}/
      - MINIO_LOCAL_GDMCOM_CUSTOM_HOST=http://minio-{{.CI_COMMIT_REF_SLUG}}:9000
      - MINIO_LOCAL_GDMCOM_BUCKET_NAME={{.minio_gdmcom_bucket}}
      - MINIO_LOCAL_GIBY_USER={{.minio_gdmcom_typo3_user}}
      - MINIO_LOCAL_GIBY_PASSWORD={{.minio_gdmcom_typo3_password}}
      - MINIO_LOCAL_GIBY_PUBLIC_URL={{.minio_giby_domain}}/{{.minio_giby_bucket}}/
      - MINIO_LOCAL_GIBY_CUSTOM_HOST=http://minio-{{.CI_COMMIT_REF_SLUG}}:9000
      - MINIO_LOCAL_GIBY_BUCKET_NAME={{.minio_giby_bucket}}
      - MYSQL_HOST={{.db_host}}
      - MYSQL_DATABASE={{.db_database}}
      - MYSQL_USER={{.mariadb_id}}
      - MYSQL_PASSWORD={{.mariadb_password}}
      - TYPO3_CONTEXT={{.TYPO3_CONTEXT}}
      - SENTRY_RELEASE={{.GIT_TAG_SENTRY}}
    depends_on:
      mariadb-{{.CI_COMMIT_REF_SLUG}}:
        condition: service_healthy
      minio-{{.CI_COMMIT_REF_SLUG}}:
        condition: service_started
    logging:
      driver: "gelf"
      options:
        gelf-address: "udp://logs.mogic.com:12201"

volumes:
  mariadb-data:

networks:
  default:
    name: traefik
    external: true
