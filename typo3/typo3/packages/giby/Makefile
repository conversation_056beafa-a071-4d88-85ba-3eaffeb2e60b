checkstyle:
	cd ../../../ && make checkstyle

typo3-clear-cache:
	cd ../../../ && make -s typo3-clear-cache

build-frontend-assets:
	cd ../../../ && make -s build-frontend-assets

local-checkstyle:
	find Resources/Private/ -iname '*.html'\
	    | grep -v Resources/Private/Templates/Components/Content/Snippet/Snippet.html\
	    | xargs -L1 xmllint --noout
	find Resources/Private/ -iname '*.html'\
	    | grep -v Resources/Private/Templates/Components/Content/Snippet/Snippet.html\
	    | xargs -L1 xmllint --noout 2>&1\
	    | wc -l | xargs test 0 -eq
