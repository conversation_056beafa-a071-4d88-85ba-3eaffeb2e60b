<?php

use TYPO3\CMS\Extbase\Utility\ExtensionUtility;

defined('TYPO3') or die('Access denied.');

/***************
 * Fluid Components
 */
$GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['fluid_components']['namespaces']['Mogic\\Giby\\Components'] =
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::extPath(
        'giby', 'Resources/Private/Templates/Components'
    );


/***************
 * RTE preset
 */
$GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['giby']
   = 'EXT:giby/Configuration/RTE/Giby.yaml';


ExtensionUtility::configurePlugin(
    'giby',
    'LocationAreas',
    [\Mogic\Giby\Controller\LocationAreasController::class => 'map'],
    // non-cacheable actions
    [\Mogic\Giby\Controller\LocationAreasController::class => 'map'],
);
