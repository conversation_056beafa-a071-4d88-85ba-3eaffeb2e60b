const tailwindPlugin = require("tailwindcss/plugin");
const {
  getInlineIconBackroundImageStyle
} = require("../../../gdmcom/tailwind/plugins/icons");

const {
  createListStyles: gdmcomCreateListStyles
} = require("../../../gdmcom/tailwind/plugins/lists");

const createListStyles = (theme, color, icon, darkModeColor = null) => {
  if (!!darkModeColor) {
    return {
      ...gdmcomCreateListStyles(theme, color, icon),
      "@media (prefers-color-scheme: dark)": {
        '& > li:before': {
          ...getInlineIconBackroundImageStyle({
            icon,
            color: darkModeColor
          })
        }
      }
    }
  }

  return {
    ...gdmcomCreateListStyles(theme, color, icon),
  }
}

const listingPlugin = tailwindPlugin(function ({
  addComponents,
  addUtilities,
  theme
}) {
  addComponents({
    ".list-check": createListStyles(theme, "#00b1b2", "check"),
    ".list-check-circle": createListStyles(theme, "#000000", "check-circle", "#ffffff")
  });

  addUtilities({
    ".list-aligned": {
      display: "table",
    },
    ".list-centered": {
      margin: "0 auto",
    },
    ".list-right": {
      margin: "0 0 0 auto",
    },
  });
});

module.exports = {
  listingPlugin,
};
