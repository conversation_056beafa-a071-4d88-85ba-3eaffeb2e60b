const tailwindPlugin = require("tailwindcss/plugin");
const {
  getInlineIconBackroundImageStyle
} = require("../../../gdmcom/tailwind/plugins/icons");

const {
  createIconLinkStyle: gdmComCreateIconLinkStyle,
} = require("../../../gdmcom/tailwind/plugins/links");


const createIconLinkStyle = (icon) => {
  return {
    ...gdmComCreateIconLinkStyle(icon),
    "@media (prefers-color-scheme: dark)": {
      '&:before': {
        ...getInlineIconBackroundImageStyle({
          color: "#ffffff",
          icon
        })
      },
      '&:hover': {
        color: "#66d0d1",
        '&:before': {
          ...getInlineIconBackroundImageStyle({
            color: "#66d0d1",
            icon
          })
        }
      }
    }
  }
}


const linkPlugin = tailwindPlugin(function ({ addComponents, theme }) {
  addComponents({
    '.inline-link': createIconLinkStyle("globe"),
    '.inline-link-file': createIconLinkStyle("arrow-down-to-bracket"),
    '.inline-link-phone': createIconLinkStyle("phone"),
    '.inline-link-email': createIconLinkStyle("mail")
  })
});

module.exports = {
  linkPlugin,
  createIconLinkStyle
};
