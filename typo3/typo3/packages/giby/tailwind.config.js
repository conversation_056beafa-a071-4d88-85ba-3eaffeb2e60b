const safelist = require("./tailwind/safelist");
const plugins = require("./tailwind/plugins");
const extendedColors = require("../gdmcom/tailwind/colors");

/** @type {import('tailwindcss').Config} */
module.exports = {
  presets: [require("../gdmcom/tailwind/presets/base.config")],
  darkMode: "media",
  theme: {
    extend: {
      colors: {
        ...extendedColors,
      },
      keyframes: {
        "border-spin": {
          "0%": {
            "--angle": "0deg",
          },

          "100%": {
            "--angle": "450deg",
          },
        },
      },
      animation: {
        "border-spin": "border-spin 1.5s ease-in-out",
      },
      backgroundImage: {
        "conic-gradient":
          "conic-gradient(from var(--angle), white, white, white, var(--primary-500))",
      },
    },
  },
  content: [
    "../gdmcom/Resources/Private/Layouts/**/*.html",
    "../gdmcom/Resources/Private/Partials/**/*.html",
    "../gdmcom/Resources/Private/Components/**/*.html",
    "../gdmcom/Resources/Private/Templates/**/*.html",
    "./Resources/Private/Layouts/**/*.html",
    "./Resources/Private/Partials/**/*.html",
    "./Resources/Private/Components/**/*.html",
    "./Resources/Private/Templates/**/*.html",
  ],
  safelist,
  plugins,
};
