<?php

defined('TYPO3') or die();


call_user_func(static function () {
    $additionalFields = [
        "tx_giby_tab_container_main_tab_label" => [
            'exclude' => 1,
            'label' => 'Tab Label (Hauptinhalt)',
            'description' => 'Label für Umschalter (Hauptinhalt)',
            'config' => [
                'type' => 'input',
                'default' => 'Hauptinhalt',
            ],
        ],
        "tx_giby_tab_container_secondary_tab_label" => [
            'exclude' => 1,
            'label' => 'Tab Label (versteckter Inhalt)',
            'description' => 'Label für Umschalter (versteckter Inhalt)',
            'config' => [
                'type' => 'input',
                'default' => 'versteckter Inhalt',
            ],
        ],
    ];

    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns(
        'tt_content',
        $additionalFields
    );

    $containerConfigurations = [
        'tx_giby_tab_container_one_column' => [
            1,
            ['Tab Container (1 Spalte)', 'Tab Container mit einer Inhaltsspalte'],
            [
                "tx_gdmcom_container_content_centered"
            ]
        ],
        'tx_giby_tab_container_two_columns' => [
            2,
            ['Tab Container (2 Spalte je 50%)', ' Tab Container mit 2 Inhaltsspalten (50%)', 2],
            []
        ],
        'tx_giby_tab_container_three_columns' => [
            3,
            ['Tab Container (3 Spalten je 33%])', 'Tab Container 3 Inhaltsspalten (33%)'],
            []
        ],
        'tx_giby_tab_container_four_columns' => [
            4,
            ['Tab Container (4 Spalten je 25%])', 'Tab Container 4 Inhaltsspalten (25%)'],
            [
            ]
        ]
    ];

    $standardFieldTypesForContainer = [
        'CType',
        'header',
        'tx_giby_tab_container_main_tab_label',
        'tx_giby_tab_container_secondary_tab_label',
        'tx_gdmcom_container_background_color',
        'tx_gdmcom_container_spacing_top',
        'tx_gdmcom_container_spacing_bottom',
    ];

    $containerConfigurationKeys = array_keys($containerConfigurations);

    $standardColumnConf = [
        'name' => 'Spalteninhalt ',
        'label' => "Test",
        'colPos' => 100,
        'disallowed' => [
            'CType' => implode(",", $containerConfigurationKeys)
        ]
    ];

    $containerConfigurations = array_map(
        function ($key, $conf) use ($standardColumnConf, $standardFieldTypesForContainer) {
            $columnsConf = array_fill(0, $conf[0], $standardColumnConf);
            $mainColumns = array_map(
                function ($index, $column) {
                    $column['name'] .= " (Hauptbereich) #" . $index + 1;
                    $column['colPos'] = $column['colPos'] + $index + 1;
                    return $column;
                },
                array_keys($columnsConf),
                $columnsConf
            );

            $secondaryColumns = array_map(
                function ($index, $column) {
                    $column['name'] .= " (versteckter Bereich) #" . $index + 1;
                    $column['colPos'] = ($column['colPos']*2) + $index + 1;
                    return $column;
                },
                array_keys($columnsConf),
                $columnsConf
            );
            return (object) [
                "key" => $key,
                "icon" => str_replace("tx_giby_", "", $key),
                "label" => $conf[1][0],
                "description" => $conf[1][1],
                "grid" => [
                    $mainColumns,
                    $secondaryColumns
                ],
                'showitems' => array_merge($standardFieldTypesForContainer, $conf[2])
            ];
        },
        $containerConfigurationKeys,
        $containerConfigurations
    );

    // WIZARD CONTAINER
    $wizardRowNames = [
        "Produktvorschlag #1",
        "Produktvorschlag #2",
        "Produktvorschlag #3"
    ];

    $rows = array_map(
        function($index, $name) {
            return [
                [
                    'name' => $name,
                    'colPos' => ($index + 1) * 100,
                    'colspan' => 1,
                    'allowed' => [
                        'CType' => 'mask_product_card'
                    ],
                    'maxitems' => 1
                ]
            ];
        },
        array_keys($wizardRowNames),
        $wizardRowNames
    );
    $containerConfigurations[] = (object) [
        "key"           => "tx_giby_wizard_container",
        "icon"          => "wizard",
        "label"         => "Wizard Container",
        "description"   => "Wizard Container",
        "grid"          => $rows,
        "showitems"     => [
            'CType',
            'header'
        ]
    ];
    // WIZARD CONTAINER END

    $containerRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(
        \B13\Container\Tca\Registry::class
    );

    foreach ($containerConfigurations as $container) {
        $containerRegistry->configureContainer(
            (
                new \B13\Container\Tca\ContainerConfiguration(
                    $container->key,
                    $container->label,
                    $container->description,
                    $container->grid
                )
            )->setIcon(
                "EXT:giby/Resources/Public/Icons/Theme/Container/{$container->icon}.svg"
            )->setSaveAndCloseInNewContentElementWizard(true)
        );

        $GLOBALS['TCA']['tt_content']['types'][$container->key]['showitem'] = implode(",", $container->showitems);
    }
});

$containerBackgroundSelectItems = $GLOBALS['TCA']['tt_content']['columns']["tx_gdmcom_container_background_color"]["config"]["items"];
foreach ($containerBackgroundSelectItems as $key => $containerBackgroundSelectItemConfig) {
    $containerBackgroundSelectItemConfig["icon"] = str_replace("gdmcom", "giby", $containerBackgroundSelectItemConfig["icon"]);
    $GLOBALS['TCA']['tt_content']['columns']["tx_gdmcom_container_background_color"]["config"]["items"][$key] = $containerBackgroundSelectItemConfig;
}