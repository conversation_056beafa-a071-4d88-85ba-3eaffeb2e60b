page = PAGE
page {
    shortcutIcon = {$giby.theme.favicon_path}/favicon.ico
    typeNum = 0

    meta {
        viewport = width=device-width, initial-scale=1, shrink-to-fit=no

        X-UA-Compatible = IE=edge
        X-UA-Compatible.attribute = http-equiv

        content-language = de-de
        content-language.attribute = http-equiv

        msapplication-TileColor = {$giby.theme.color}
        theme-color = {$giby.theme.color}
    }

    headerData {
        10 = COA
        10 {
            1 < lib.favicons.link
            1.file = {$giby.theme.favicon_path}/apple-touch-icon.png
            1.stdWrap.wrap = <link rel="apple-touch-icon" sizes="180x180" href="|">

            2 < lib.favicons.link
            2.file = {$giby.theme.favicon_path}/favicon-32x32.png
            2.stdWrap.wrap = <link rel="icon" type="image/png" sizes="32x32" href="|">

            3 < lib.favicons.link
            3.file = {$giby.theme.favicon_path}/favicon-16x16.png
            3.stdWrap.wrap = <link rel="icon" type="image/png" sizes="16x16" href="|">
        }
    }
    
    20 = FLUIDTEMPLATE
    20 {
        templateRootPaths.21 = EXT:giby/Resources/Private/Templates/
        partialRootPaths.21  = EXT:giby/Resources/Private/Partials/
        layoutRootPaths.21   = EXT:giby/Resources/Private/Layouts/

        file.stdWrap.cObject = CASE
        file.stdWrap.cObject {
            key.field = doktype

            default = TEXT
            default.value = EXT:giby/Resources/Private/Templates/Page/Default.html
        }

        dataProcessing {
            # Main manu
            10 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            10 {
                special = directory
                special.value = {$brand.pids.main_menu}
                levels = 3
                includeSpacer = 0
                as = mainnavigation
            }

            # Footer Menu
            40 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            40 {
                special = directory
                special.value = {$brand.pids.footer_menu}
                levels = 3
                includeSpacer = 1
                as = footernavigation
            }
        }
        variables {
            copyright = TEXT
            copyright {
                value = {$giby.theme.copyright}
            }

            socialmediaLinksUid = TEXT
            socialmediaLinksUid {
                value = {$brand.pids.socialmedialinks}
            }

            contactUid = TEXT
            contactUid {
                value = {$brand.pids.contact}
            }

            entrypointUid = TEXT
            entrypointUid {
                value = {$brand.pids.entrypoint}
            }

            contactButtonTitle = TEXT
            contactButtonTitle {
                value = {$brand.other.contact_button_title}
            }

            footerDescription = TEXT
            footerDescription {
                value = {$brand.footer.description}
            }
        }
    }
}
