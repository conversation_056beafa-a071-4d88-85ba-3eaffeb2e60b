lib.containerElement {
  templateRootPaths.20 = EXT:giby/Resources/Private/Templates/Container
}

tt_content.tx_giby_tab_container_one_column < lib.containerElement
tt_content.tx_giby_tab_container_one_column.templateName = MainTabContainer

tt_content.tx_giby_tab_container_two_columns < lib.containerElement
tt_content.tx_giby_tab_container_two_columns.templateName = TwoColumnTabContainer

tt_content.tx_giby_tab_container_three_columns < lib.containerElement
tt_content.tx_giby_tab_container_three_columns.templateName = ThreeColumnTabContainer

tt_content.tx_giby_tab_container_four_columns < lib.containerElement
tt_content.tx_giby_tab_container_four_columns.templateName = FourColumnTabContainer

tt_content.tx_giby_wizard_container < lib.containerElement
tt_content.tx_giby_wizard_container.templateName = WizardContainer