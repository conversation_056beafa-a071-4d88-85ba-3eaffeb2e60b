function load_matomo_script(e) {
    // save user's choice in a local variable
    const matomoServiceConsent = event.detail?.services["u6fxocwTs"]?.consent?.given ?? false

    // check if our service has been accepted
    if(!!matomoServiceConsent) {
        var _paq = window._paq = window._paq || [];
        /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
        _paq.push(['trackPageView']);
        _paq.push(['enableLinkTracking']);
        (function() {
        var u="//statistics.gdmcom-gruppe.de/";
        _paq.push(['setTrackerUrl', u+'matomo.php']);
        _paq.push(['setSiteId', matomoSiteId]);
        var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
        g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
        })();
    }
}

window.addEventListener('UC_CONSENT', load_matomo_script)
