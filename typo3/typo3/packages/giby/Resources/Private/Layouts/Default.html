<html data-namespace-typo3-fluid="true"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:gibyc="http://typo3.org/ns/Mogic/Giby/Components"
  >
  <div class="min-h-screen flex flex-col w-full pt-[var(--header-height)]">
      <f:render partial="Header" section="Main" arguments="{_all}" />
      <div class="group/page max-w-[1440px] mx-auto px-content flex-1 w-full">
          <f:render section="Hero" />
          <f:if condition="{data.uid} != {entrypointUid}">
            <f:render section="Main" partial="Breadcrumb" arguments="{_all}" />
          </f:if>
          <f:render section="Main" />
      </div>
      <f:render partial="Footer" section="Main" arguments="{_all}" />
  </div>
</html>