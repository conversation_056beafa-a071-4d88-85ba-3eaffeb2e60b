import "../Css/main.css";
import Alpine from "alpinejs";

import modal from "./modules/modal";
import switcher from "./modules/switcher";
import areaMap from "./modules/areaMap";
import wizard from "./modules/wizard";
import { showCookiebotBannerSettingsBasedOnHash, showCookiebotBannerSettings } from "./utils/cookiebot";

Alpine.prefix("x-giby-");
Alpine.data("modal", modal);
Alpine.data("switcher", switcher);
Alpine.data("areaMap", areaMap);
Alpine.data("wizard", wizard);

Alpine.start();

const cookie_banner_show_settings_hash = "#show_cookiebanner_settings"
showCookiebotBannerSettingsBasedOnHash(cookie_banner_show_settings_hash)
window.addEventListener("hashchange", () => showCookiebotBannerSettingsBasedOnHash(cookie_banner_show_settings_hash))

window.cookiebot_show_settings = showCookiebotBannerSettings
