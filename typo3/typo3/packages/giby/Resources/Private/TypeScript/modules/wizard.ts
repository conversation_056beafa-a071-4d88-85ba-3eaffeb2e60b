import { defineComponent } from "../../../../../gdmcom/Resources/Private/TypeScript/utils/alpinejs";
import { toggleList } from "../../../../../gdmcom/Resources/Private/TypeScript/lib/utilities";

export default defineComponent((data: IWizardData) => ({
  currentStepIndex: 0,
  stepsData: data.steps as IStep[],
  selections: [[]] as number[][],

  card: {
    ["x-giby-on:click"]() {
      const self = this;
      const target = self as typeof self & IItemData;
      let toggleCallback = this.toggleSelectCard;
      if (this.isCurrentStepMultiselect) {
        toggleCallback = this.toggleSelectMultipleCards;
      }

      toggleCallback.call(this, target.points);
    },

    ["x-giby-bind:class"]() {
      const self = this;
      const target = self as typeof self & IItemData;

      return this.isSelected(target.points)
        ? "ring ring-primary-600 dark:ring-primary-200"
        : "";
    },
  },

  stepItemBinding: {
    ["x-giby-bind:class"]() {
      const self = this;
      const target = self as typeof self & IStepData;

      return target.stepIndex !== this.currentStepIndex ? "hidden" : "flex";
    },
  },

  suggestedProductBinding: {
    ["x-giby-bind:class"]() {
      switch (true) {
        case this.finalPoints < 5:
          return "[&>*:nth-child(1)]:block [&>*]:hidden";
        case this.finalPoints < 10:
          return "[&>*:nth-child(2)]:block [&>*]:hidden";
        case this.finalPoints >= 10:
          return "[&>*:nth-child(3)]:block [&>*]:hidden";
      }
    },
    ["x-giby-on:animationend"](event: Event) {
      const target: HTMLElement = event.currentTarget as HTMLElement;
      target.classList.remove("before:animate-border-spin");
      target.classList.add("bg-primary-500");
    },
  },

  navItemBinding: {
    ["x-giby-bind:class"]() {
      const self = this;
      const target = self as typeof self & INavItemStepData;
      const isActiveStep = target.navItemIndex === this.currentStepIndex;
      const isCompleted = target.navItemIndex < this.currentStepIndex;

      if (isActiveStep) {
        return "is-active";
      }
      if (isCompleted) {
        return "is-completed";
      }

      return "is-untouched";
    },
  },

  progressBarBinding: {
    ["x-giby-bind:style"]() {
      const self = this;
      const target = self as typeof self & INavItemStepData;
      const isCompleted = target.navItemIndex < this.currentStepIndex;

      return isCompleted
        ? "--progressbar-width: 100%"
        : "--progressbar-width: 0%";
    },
  },

  ctaBinding: {
    ["x-giby-bind:class"]() {
      return this.isFinalStep ? "hidden" : "";
    },

    ["x-giby-bind:disabled"]() {
      return (
        !this.isFinalStep && this.selections[this.currentStepIndex].length === 0
      );
    },
  },

  init() {
    this.initializeWizard();
  },

  goToNextStep() {
    if (this.currentStepIndex < this.stepsData.length) {
      this.currentStepIndex++;
    }
  },

  goToPrevStep() {
    this.currentStepIndex--;
  },

  isSelected(id: number) {
    return this.selections[this.currentStepIndex].includes(id);
  },

  get isFinalStep() {
    return this.currentStepIndex === this.stepsData.length;
  },

  get isCurrentStepMultiselect() {
    return !!this.stepsData[this.currentStepIndex].multiselect;
  },

  get finalPoints() {
    // ...[0, ...cur] is a hack to avoid Math.max returning -Infinity if one of the arrays is empty
    const finalPoints = this.selections.reduce(
      (prev, cur) => prev + Math.max(...[0, ...cur]),
      0
    );

    return finalPoints;
  },

  get getCompletedSteps() {
    return this.selections.map((selection) => selection.length > 0);
  },

  toggleSelectCard(id: number) {
    const selection = [];
    if (!this.selections[this.currentStepIndex].includes(id)) {
      selection.push(id);
    }

    this.selections[this.currentStepIndex] = selection;
  },

  toggleSelectMultipleCards(id: number) {
    this.selections[this.currentStepIndex] = toggleList(
      this.selections[this.currentStepIndex],
      id
    );
  },

  initializeWizard() {
    this.selections = Array.from(
      Array(this.stepsData.length),
      () => new Array()
    );
  },

  reset() {
    this.initializeWizard();
    this.currentStepIndex = 0;
  },
}));
