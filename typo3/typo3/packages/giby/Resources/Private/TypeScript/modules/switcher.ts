import { defineComponent } from "../../../../../gdmcom/Resources/Private/TypeScript/utils/alpinejs";

export default defineComponent((data: ISwitcherData) => ({
  activeTab: data.initialTab,

  changeTab: {
    ["x-giby-on:click"]() {
      const tabId = this.currentTab;
      if (!tabId || this.activeTab === tabId) return;
      this.activeTab = tabId;
    },
    ["x-giby-bind:class"]() {
      return this.activeTab === this.currentTab
        ? "bg-black dark:bg-gray-200 text-white dark:text-black hover:bg-black/80 dark:hover:bg-gray-400"
        : "";
    },
  },

  get currentTab(): string {
    return this.$el.getAttribute("data-tab") ?? data.initialTab;
  },
}));
