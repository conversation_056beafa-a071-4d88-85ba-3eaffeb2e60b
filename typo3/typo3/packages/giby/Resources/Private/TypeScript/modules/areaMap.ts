import { GeoJSON, geoJson, Map, Path, LatLngBounds, LatLng, DomEvent } from 'leaflet';
import { defineComponent } from '../../../../../gdmcom/Resources/Private/TypeScript/utils/alpinejs'
import { FeatureCollection, Feature } from 'geojson';
import { ILocationAreaItemData, TLocationAreaFeature, IMapAreaData } from '../../../../../gdmcom/Resources/Private/TypeScript/types/maps';
import { CONF_STYLE_BY_FEATURE, FEATURE_STYLE, getCenterFromCountry, resetMap } from '../lib/leaflet'
import { createPattern } from '../lib/patterns';

const ym = window.ym ?? null

export default defineComponent(
  (data: IMapAreaData) => ({
    map: {} as Map,
    activeAreaUid: "",
    activeLayer: {} as Record<string, Path>,
    activeFeature: {} as Record<string, TLocationAreaFeature>,
    activeFilterModel: data.activeFilterModel ?? [],
    mapInitialized: false,
    geoJsonLayer: {} as GeoJSON,
    options: {
      zoom: 6,
      center: getCenterFromCountry("germany"),
      ...data.options
    },
    styleConf: CONF_STYLE_BY_FEATURE,

    areaFilterBinding: {
      ['x-giby-on:change'] (event: any) {
        (this.$refs.form as HTMLFormElement).submit();
      },
      ['x-giby-on:reset-filter'](event: any) {
        if (event.detail) {
          const checkboxList = this.$el.querySelectorAll(
            `input[name="${event.detail}"]`
          )

          checkboxList.forEach(checkbox => {
            (checkbox as HTMLInputElement).checked = false
          });
          (this.$el as HTMLFormElement).submit();
        }
      }
    },

    locationAreaItemBinding: {
      'x-giby-bind:class'() {
        const self = this
        const target = self as (typeof self) & ILocationAreaItemData
        const {
          activeAreaUid,
          uid
        } = target

        return {
          'hidden': activeAreaUid != uid
        }
      },
    },

    init() {
      if (!ym) {
        return
      }

      this.mapInitialized = true
      ym.ready(
        () => {
          this.map = ym.map(this.mapElement, {
            center: this.options.center,
            zoom: this.options.zoom
          }) as Map
          this.updateAreas(data.areaData)
        }
      )
    },

    updateAreas(data: FeatureCollection) {
      const _self = this
      if (data.features.length === 0) {
        resetMap(
          _self.map,
          {
            zoom: this.options.zoom,
            center: this.options.center
          }
        )
        return
      }

      this.styleConf = this.styleConf.map(
        conf => {
          const id = `${conf.slug}-pattern`
          const { node, objs } = createPattern(id, conf)
          this.mapElement.append(node as SVGElement)

          return {
            ...conf,
            ids: objs
          }
        }
      )

      this.geoJsonLayer = geoJson(
        data,
        {
          onEachFeature(feature: TLocationAreaFeature, layer: Path & { _leaflet_id: string }) {
            const { slug, style } = _self.getStyleFromFeature(feature);
            layer._leaflet_id = feature.properties.uid
            layer.setStyle(style)
            layer.options.className = `${slug}-pattern-fill`;
            layer.on("click", _self.showLocationCard.bind(
                _self,
                feature,
                layer
            ))
          },
        }
      ).addTo(_self.map)
      _self.fitBoundsToGeoJsonLayer()
    },

    fitBoundsToGeoJsonLayer() {
      this.map.fitBounds(this.currentBounds, {
        padding: [5,5]
      })
    },

    getStyleFromFeature(feature: TLocationAreaFeature): FEATURE_STYLE {
      const style = this.styleConf.find(
        (style: FEATURE_STYLE) => style.label === feature.properties.status
      ) as FEATURE_STYLE
      return style
    },

    showLocationCard(feature: TLocationAreaFeature, layer: Path, event: any) {
      if (event.originalEvent.target.nodeName !== 'path') {
          return;
      }

      DomEvent.stopPropagation(event)
      this.resetActiveLayer()
      layer.getElement()?.setAttribute("data-hover", "1")
      layer.setStyle({
        color: "#000"
      })

      this.activeAreaUid = feature.properties.uid
      this.activeLayer[feature.properties.uid] = layer
      this.activeFeature[feature.properties.uid] = feature

      const latlngs = [...feature.geometry.coordinates[0]].map(
        ([lng, lat]) => new LatLng(lat, lng)
      )
      const bounds = new LatLngBounds(latlngs)
      const ymBounds = ym.latLngBounds(bounds.getSouthWest(), bounds.getNorthEast())

      this.map.fitBounds(ymBounds, {
        paddingBottomRight: [380,50],
        // paddingTopLeft: [5,5],
        maxZoom: 17
      })
    },

    hideLocationCard(resetToGeoJsonBounds: boolean = true) {
      this.resetActiveLayer()
      this.activeAreaUid = ""

      if (!!resetToGeoJsonBounds) {
        this.fitBoundsToGeoJsonLayer()
      }
    },

    resetActiveLayer() {
      const activeLayer = this.activeLayer[this.activeAreaUid]
      if (activeLayer) {
        const {
          style: {
            color
          }
        } = this.getStyleFromFeature(this.activeFeature[this.activeAreaUid])
        activeLayer.getElement()?.setAttribute("data-hover", "0")
        activeLayer.setStyle({
          color: color
        })
      }
    },

    get currentBounds(): LatLngBounds {
      const bounds = this.geoJsonLayer.getBounds();
      return ym.latLngBounds(bounds.getSouthWest(), bounds.getNorthEast(), )
    },

    get mapElement(): HTMLElement {
      return this.$refs.map as HTMLElement
    },
  })
)
