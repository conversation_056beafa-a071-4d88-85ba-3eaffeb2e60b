class Id {
    id: string;
    href: string;
    constructor(id: string) {
        this.id = id;
        this.href = new URL(`#${id}`, location.href) + "";
    }
}
  
Id.prototype.toString = function() {
    return "url(" + this.href + ")";
};

var count = 0

function uid(name: string) {
    const identifier = ++count
    return new Id("O-" + (name == null ? "" : name + "-") + identifier);
}

export default {
    uid,
}