import { LatLng, Map } from "leaflet";

export function getCenterFromCountry(country: "germany"): LatLng {
  return new LatLng(51.163361, 10.447683);
}

export type TResetMapOptions = {
  zoom: number;
  center: LatLng;
};
export function resetMap(map: Map, options: TResetMapOptions) {
  map.setZoom(options.zoom);
  map.panTo(options.center);
}

export type FEATURE_STYLE = {
  ids?: {
    hover: object;
    default: object;
  };
  slug: string;
  label: string;
  style: {
    fillColor: string;
    color: string;
    fillOpacity: number;
    weight: number;
  };
};

export const CONF_STYLE_BY_FEATURE: FEATURE_STYLE[] = [
  {
    slug: "progress",
    label: "In Bearbeitung",
    style: {
      color: "#998500",
      fillColor: "#FFDD00",
      fillOpacity: 0.49,
      weight: 1.5,
    },
  },
  {
    slug: "planning",
    label: "In Planung",
    style: {
      fillColor: "#F08539",
      color: "#8E3D05",
      fillOpacity: 0.49,
      weight: 1.5,
    },
  },
  {
    slug: "completed",
    label: "Abgeschlossen",
    style: {
      fillColor: "#66D0D1",
      color: "#006A6B",
      fillOpacity: 0.49,
      weight: 1.5,
    },
  },
];
