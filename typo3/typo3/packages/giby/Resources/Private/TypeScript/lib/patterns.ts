import * as d3 from "d3"
import { FEATURE_STYLE } from "./leaflet";
import DOM from "./DOM";

export function createPattern(id: string, styleConf: FEATURE_STYLE, angle: number = 45)
{
    const {
        style: {
            weight,
            color,
            fillColor
        },
    } = styleConf

    const patternWeight = weight * 2.5

    const objs = {
        default: DOM.uid(`${id}-default`),
        hover: DOM.uid(`${id}-hover`)
    }
    const svg = d3
        .create("svg")
        .attr("width", 0)
        .attr("height", 0);
    
    const defs = svg.append("defs");
    const defaultPattern = defs.append("pattern")
        .attr("id", objs.default.id)
        // .attr("x", 0)
        // .attr("y", 0)
        .attr("width", patternWeight)
        .attr("height", patternWeight)
        .attr("patternUnits", "userSpaceOnUse")
        // .attr("patternContentUnits", "userSpaceOnUse")
        .attr("patternTransform", `rotate(${angle})`);

    [fillColor, color].forEach(
        (color, index) => {
            defaultPattern.append("line")
                .attr("stroke", color)
                .attr("stroke-width", patternWeight)
                .attr("stroke-linecap", "round")
                .attr("stroke-linejoin", "round")
                .attr("fill", "none")
                .attr("pointer-events", "none")
                .attr("x1", index * patternWeight)
                .attr("y", index * patternWeight)
                .attr("x2", index * patternWeight)
                .attr("y2", (index + 1) * patternWeight)
        }
    )

    const hoverPattern = defs.append("pattern")
        .attr("id", objs.hover.id)
        // .attr("x", 0)
        // .attr("y", 0)
        .attr("width", patternWeight)
        .attr("height", patternWeight)
        .attr("patternUnits", "userSpaceOnUse")
        // .attr("patternContentUnits", "userSpaceOnUse")
        .attr("patternTransform", `rotate(${angle})`);

    [fillColor, "#000000"].forEach(
        (color, index) => {
            hoverPattern.append("line")
                .attr("stroke", color)
                .attr("stroke-width", patternWeight)
                .attr("stroke-linecap", "round")
                .attr("stroke-linejoin", "round")
                .attr("fill", "none")
                .attr("pointer-events", "none")
                .attr("x1", index * patternWeight)
                .attr("y", index * patternWeight)
                .attr("x2", index * patternWeight)
                .attr("y2", (index + 1) * patternWeight)
        }
    )

    const stylesheet = document.styleSheets[0];
    stylesheet.insertRule(`.${id}-fill { fill: ${objs.default}; }`)
    stylesheet.insertRule(`.${id}-fill[data-hover="1"], .${id}-fill:hover { fill: ${objs.hover}; stroke: #000;}`)
    return {
        node: svg.node(),
        objs
    }
}
