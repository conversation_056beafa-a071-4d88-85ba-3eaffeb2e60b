<html data-namespace-typo3-fluid="true"
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
    xmlns:v="http://typo3.org/ns/TYPO3/CMS/vhs/ViewHelpers"
    xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
    xmlns:x-on="http://example.org/dummy-ns"
    xmlns:x-bind="http://example.org/dummy-ns"
    xmlns:x-show="http://example.org/dummy-ns"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
>
  <f:layout name="DefaultPlugin" />

  <f:section name="Form">
    <form x-giby-ref="form" action="{action}" x-giby-bind='areaFilterBinding' class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 z-10">
      <f:for each="{options}" key="filterKey" as="values" iteration="iterator">
        <f:switch expression="{filterKey}">
          <f:case value="status">
            <f:variable name="label" value="Status Baufortschritt" />
            <f:variable name="icon" value="services/bandwidth.svg" />
          </f:case>
          <f:case value="areas">
            <f:variable name="label" value="Standorte" />
            <f:variable name="icon" value="default/map-pin.svg" />
          </f:case>
        </f:switch>
          <gdmcomc:dropdown
            label="{label}"
            icon="{icon}"
            name="tx_giby_locationareas[{filterKey}][]"
            options="{values}"
            selected="{selected.{filterKey}}"
          />
      </f:for>
    </form>
  </f:section>

  <f:section name="Item">

    <f:variable name="itemData"
      value="{
        uid: locationArea.uid
      }" />

    <f:variable name="itemJsonData"
      value="{itemData -> v:format.json.encode()}" />
    <div
      x-giby-data="{itemJsonData}"
      x-giby-bind="locationAreaItemBinding"
      class="flex absolute top-0 right-0 lg:top-5 lg:bottom-auto lg:right-5 bottom-0 w-full lg:w-[320px] z-[1001]"
      x-giby-cloak=""
    >
      <gdmcomc:content.card
        header="{
            headline: locationArea.name,
            layout: 106
          }"
        class="!border-gray-200 shadow-none outline outline-white outline-4 md:outline-none"
      >
        <fc:content slot="actions">
          <button
            class="inline-flex items-center w-full hover:underline gap-x-2"
            x-giby-on:click="hideLocationCard"
          >
            <gdmcomc:icon name="default/arrow-left" class="size-4" />
            Details ausblenden
          </button>
        </fc:content>
        <fc:content>
          <div class="flex flex-col gap-4 leading-normal">
            <ul class="list-none">
              <f:if condition="{locationArea.status}">
                <li><span class="font-bold">Netzausbau:</span> {locationArea.status}</li>
              </f:if>
              <f:if condition="{locationArea.established}">
                <li><span class="font-bold">Inbetriebnahme:</span> {locationArea.established}</li>
              </f:if>
            </ul>
          </div>
        </fc:content>
      </gdmcomc:content.card>
    </div>
  </f:section>

  <f:section name="Main">
    <f:if condition="{geoJson}">      
      <f:if condition="{settings.yellowmaps.scriptUrlWithApiKey}">
        <f:asset.script src="{settings.yellowmaps.scriptUrlWithApiKey}"
            priority="false"
            identifier="yellowmap"
        />
      </f:if>
      <f:variable name="mapsData" value="{
          options: options,
          areaData: geoJson
      }" />
      <div x-giby-data="areaMap({mapsData -> v:format.json.encode()})"
        x-giby-cloak=""
        class="relative overflow-hidden flex flex-col gap-8"
      >
        <f:render section="Form" arguments="{
          options: {
            status: status,
            areas: areas
          },
          selected: selected,
          action: '#c{contentUid}'
        }" />
        <div class="aspect-[2/3] lg:aspect-square xl:aspect-[2/1] relative z-0 rounded-lg overflow-hidden">
          <div x-giby-ref="map" style="height: 100%;"></div>
          <f:for each="{locationAreas}" as="locationArea" iteration="iterator">
            <f:render section="Item" arguments="{
              locationArea: locationArea,
              iterator: iterator,
            }" />
          </f:for>
        </div>
        <ul class="flex flex-wrap gap-x-8 gap-y-4 text-sm">
          <li class="inline-flex gap-x-4 items-center"><span class="rounded-full bg-orange-500 border border-orange-600 size-6"></span>In Bearbeitung</li>
          <li class="inline-flex gap-x-4 items-center"><span class="rounded-full bg-yellow-600 border border-yellow-700 size-6"></span>In Planung</li>
          <li class="inline-flex gap-x-4 items-center"><span class="rounded-full bg-primary-500 border border-primary-600 size-6"></span>Fertig</li>
        </ul>
      </div>
    </f:if>
  </f:section>
</html>
