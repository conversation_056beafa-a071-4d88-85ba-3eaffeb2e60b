<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:giby="http://typo3.org/ns/Mogic/Giby/Components"
  data-namespace-typo3-fluid="true"
>
  <f:layout name="Default" />

  <f:section name="Footer">
    <f:variable name="buttonTheme" value="secondary" />
    <f:if condition="{marked-as}">
      <f:variable name="buttonTheme" value="primary" />
    </f:if>

    <f:variable name="sizeClass" value="min-h-[187px]" />
    <f:if condition="{shrink} == 1">
      <f:variable name="sizeClass" value="" />
    </f:if>
    <f:if condition="{orientation} == 1">
      <f:variable name="sizeClass" value="{sizeClass} 2xl:min-w-[272px]" />
    </f:if>

    <div class="flex flex-col justify-end gap-4 {sizeClass}">
      <f:if condition="{price} || {modal.textContent}">
        <div class="flex flex-wrap justify-center gap-2.5 items-center">
          <f:if condition="{priceBefore}">
            <span
              class="leading-none line-through whitespace-nowrap font-['Verdana']"
            >
              <f:format.number
                decimals="2"
                decimalSeparator=","
                thousandsSeparator="."
              >
                {priceBefore}
              </f:format.number>
              €
            </span>
          </f:if>

          <f:if condition="{price}">
            <span
              class="text-3xl font-bold leading-none text-primary-700 whitespace-nowrap dark:text-primary-400"
            >
              <f:format.number
                decimals="2"
                decimalSeparator=","
                thousandsSeparator="."
              >
                {price}
              </f:format.number>
              €
            </span>
            <span class="font-bold">{periodicity}</span>
          </f:if>

          <f:if condition="{modal.textContent}">
            <giby:modal class="only:ml-auto">
              <f:if condition="{logo.asset}">
                <fc:content slot="header">
                  <gdmcomc:logo
                    projectName="{logo.projectName}"
                    name="{logo.asset}"
                    class="w-auto h-7"
                  />
                  <f:if condition="{logo.text}">
                    <gdmcomc:headline
                      headline="{logo.text}"
                      layout="104"
                      class="truncate"
                    />
                  </f:if>
                </fc:content>
              </f:if>

              <p class="text-base font-bold">{modal.headline}</p>
              {modal.textContent -> f:format.html()}

              <fc:content slot="cta">
                <gdmcomc:icon name="default/info-circle" class="w-5 h-5" />
              </fc:content>
            </giby:modal>
          </f:if>

          <f:if condition="{subline}">
            <span
              class="flex-none w-full font-bold text-center truncate text-primary-700 dark:text-primary-400"
              >{subline}</span
            >
          </f:if>
        </div>
      </f:if>

      <f:if condition="{link}">
        <gdmcomc:button
          link="{link}"
          no-icon="1"
          theme="{buttonTheme}"
          class="justify-center"
          placement="center"
          default-title="Verfügbarkeit prüfen"
        />
      </f:if>

      <f:if condition="{downloadLink}">
        <gdmcomc:button
          icon-before="default/arrow-down-to-bracket"
          icon-after="0"
          theme="link"
          link="{downloadLink}"
          placement="center"
          default-title="Produktdatenblatt"
        />
      </f:if>
    </div>
  </f:section>

  <f:section name="Main">
    <f:variable name="projectName" value="giby" />
    <f:variable
      name="footerArgs"
      value="{
        orientation: data.tx_mask_orientation,
        priceBefore: data.tx_mask_price_before,
        price: data.tx_mask_price,
        periodicity: data.tx_mask_periodicity,
        subline: data.tx_mask_subline,
        link: data.tx_mask_link,
        downloadLink: data.tx_mask_download,
        modal: {
          headline: data.tx_mask_modal_header,
          textContent: data.tx_mask_modal_content
        },
        logo: {
          asset: data.tx_mask_logo,
          projectName: projectName,
          text: data.tx_mask_logo_text
        },
        marked-as: data.tx_mask_highlight_label,
        shrink: data.tx_mask_shrink_footer
    }"
    />

    <f:variable name="textColumns" value="1" />
    <f:if condition="{data.tx_mask_orientation} == 1">
      <f:variable name="textColumns" value="2" />
    </f:if>
    <gdmcomc:content.card
      header="{
        headline: data.header,
        layout: data.header_layout,
        subline: data.subheader,
        overline: data.tx_mask_overline,
        truncate: 1
      }"
      logo="{
        asset: data.tx_mask_logo,
        projectName: projectName,
        text: data.tx_mask_logo_text
      }"
      icon="{data.tx_mask_icon}"
      class="group/product-card"
      orientation="{data.tx_mask_orientation}"
      marked-as="{data.tx_mask_highlight_label}"
      text-columns="{textColumns}"
      custom-border="border-gray-200 dark:border-gray-600"
      custom-shadow="shadow"
    >
      {data.bodytext -> f:format.html()}
      <fc:content slot="footer">
        <f:render section="Footer" arguments="{footerArgs}" />
      </fc:content>
    </gdmcomc:content.card>
  </f:section>
</html>
