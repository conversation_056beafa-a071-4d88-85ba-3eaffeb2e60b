<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="from" type="integer" optional="1" default="0" />
    <fc:param name="with-labels" type="boolean" optional="1" default="0" />

    <fc:renderer>
      <div class="flex flex-col gap-1 {class} w-full [--progressbar-width:0%]">
        <f:if condition="{with-labels}">
          <div
            class="flex justify-between text-xs text-gray-600 dark:text-gray-50"
          >
            <span>0%</span>
            <span>100%</span>
          </div>
        </f:if>
        <div
          class="w-full h-1 overflow-hidden bg-gray-200 rounded-full dark:bg-gray-600"
          role="progressbar"
        >
          <div
            class="h-full w-[var(--progressbar-width)] transition-[width] duration-300 ease-out delay-100 rounded-full bg-primary-500 dark:bg-primary-300"
          ></div>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
