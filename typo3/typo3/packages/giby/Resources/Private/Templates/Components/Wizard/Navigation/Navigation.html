<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:giby="http://typo3.org/ns/Mogic/Giby/Components"
  xmlns:x-giby-bind="http://example.org/dummy-ns"
  xmlns:x-giby-data="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="data" type="Array" />

    <fc:renderer>
      <div
        x-giby-data="{data -> v:format.json.encode()}"
        class="grid grid-cols-[repeat(3,_1fr)_auto] w-full gap-2"
      >
        <f:for each="{data}" as="step" iteration="stepIterator">
          <f:variable
            name="moduleData"
            value="{navItemIndex: stepIterator.index}"
          />
          <f:variable
            name="moduleDataJson"
            value="{moduleData -> v:format.json.encode()}"
          />

          <div
            x-giby-data="{moduleDataJson}"
            class="flex items-center justify-center gap-2 font-bold group/nav-item"
            x-giby-bind="navItemBinding"
          >
            <span
              class="p-1 rounded-full border-primary-500 text-black border-2 size-8 flex-none inline-flex items-center justify-center group-[.is-untouched]/nav-item:border-gray-300 group-[.is-untouched]/nav-item:font-normal group-[.is-untouched]/nav-item:border-1 group-[.is-untouched]/nav-item:text-gray-600 dark:group-[.is-untouched]/nav-item:text-gray-50 dark:text-white dark:border-primary-300 dark:bg-gray-800"
            >
              <f:if condition="{stepIterator.isLast}">
                <f:then>
                  <gdmcomc:icon
                    name="default/check"
                    class="size-full text-black dark:text-white dark:group-[.is-untouched]/nav-item:text-gray-50"
                  />
                </f:then>
                <f:else>{stepIterator.cycle}.</f:else>
              </f:if>
            </span>

            <span class="hidden lg:inline">{step.name}</span>

            <f:if condition="!{stepIterator.isLast}">
              <giby:progressbar
                class="group-[.is-completed]/nav-item:[--progressbar-width:100%]"
              />
            </f:if>
          </div>
        </f:for>
      </div>
    </fc:renderer>
  </fc:component>
</html>
