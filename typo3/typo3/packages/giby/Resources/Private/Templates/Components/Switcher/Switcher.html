<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
  xmlns:x-giby-bind="http://example.org/dummy-ns"
  xmlns:x-giby-show="http://example.org/dummy-ns"
  xmlns:x-giby-data="http://example.org/dummy-ns"
>
  <fc:component>
    <fc:param
      name="tab-label-0"
      type="string"
      optional="1"
      default="First tab"
    />
    <fc:param
      name="tab-label-1"
      type="string"
      optional="1"
      default="Second tab"
    />
    <fc:param name="initial-tab" type="string" optional="1" default="main" />

    <fc:param name="tab-content-0" type="Slot" optional="1" />
    <fc:param name="tab-content-1" type="Slot" optional="1" />

    <f:variable
      name="baseButtonClass"
      value="inline-flex items-center font-bold justify-between focus-visible:outline-none focus:ring-4 focus:ring-primary-500 rounded-lg px-8 py-2 transition-colors duration-100"
    />
    <f:variable
      name="activeClass"
      value="bg-black dark:bg-gray-200 text-white hover:bg-black/80"
    />

    <f:variable name="switcherData" value="{initialTab: initial-tab}" />

    <fc:renderer>
      <div x-giby-data="switcher({switcherData -> v:format.json.encode()})">
        <div class="w-full flex justify-center items-center">
          <div
            class="flex p-1 border-1 border-gray-300 dark:border-gray-200 rounded-lg dark:bg-gray-800"
          >
            <button
              class="{baseButtonClass}"
              data-tab="main"
              x-giby-bind="changeTab"
            >
              {tab-label-0}
            </button>
            <button
              class="{baseButtonClass}"
              data-tab="second"
              x-giby-bind="changeTab"
            >
              {tab-label-1}
            </button>
          </div>
        </div>
        <div x-giby-show="activeTab === 'main'">
          <fc:slot name="tab-content-0" />
        </div>
        <div x-giby-show="activeTab === 'second'">
          <fc:slot name="tab-content-1" />
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
