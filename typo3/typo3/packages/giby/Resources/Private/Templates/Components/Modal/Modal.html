<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="cta" type="Slot" optional="1" />
    <fc:param name="header" type="Slot" optional="1" />
    <fc:param name="footer" type="Slot" optional="1" />
    <fc:param name="closeable" type="Boolean" optional="1" default="1" />
    <f:comment>
      Not Implemented
      <fc:param
        name="placement"
        type="String"
        optional="1"
        default="center-center"
      />
    </f:comment>

    <fc:renderer>
      <div x-giby-data="modal" class="flex {class}">
        <dialog
          x-giby-ref="dialog"
          class="w-4/5 rounded-lg lg:w-1/2 max-w-[648px] backdrop:bg-gray-900 backdrop:opacity-80 backdrop:mix-blend-multiply dark:bg-gray-800 dark:text-white"
        >
          <f:if condition="{header}">
            <div
              class="relative flex items-center gap-2 py-5 pl-8 pr-16 border-gray-200 border-b-1"
            >
              <fc:slot name="header" />

              <f:if condition="{closeable}">
                <button
                  x-giby-on:click="close"
                  class="absolute top-0 bottom-0 my-auto right-8"
                >
                  <gdmcomc:icon name="default/close" class="w-6 h-6" />
                </button>
              </f:if>
            </div>
          </f:if>
          <div class="flex flex-col gap-4 px-8 py-5">
            <fc:slot />
          </div>
          <f:if condition="{footer}">
            <div class="px-8 py-5 border-gray-200 border-t-1">
              <fc:slot name="footer" />
            </div>
          </f:if>
          <f:if condition="!{header} && {closeable}">
            <button x-giby-on:click="close" class="absolute top-8 right-8">
              <gdmcomc:icon name="default/close" class="w-6 h-6" />
            </button>
          </f:if>
        </dialog>

        <button x-giby-on:click="open">
          <fc:slot name="cta" />
        </button>
      </div>
    </fc:renderer>
  </fc:component>
</html>
