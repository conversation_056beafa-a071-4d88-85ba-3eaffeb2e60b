<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:giby="http://typo3.org/ns/Mogic/Giby/Components"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
  xmlns:x-giby-bind="http://example.org/dummy-ns"
  xmlns:x-giby-data="http://example.org/dummy-ns"
  xmlns:x-giby-on="http://example.org/dummy-ns"
  xmlns:x-giby-show="http://example.org/dummy-ns"
  xmlns:x-giby-if="http://example.org/dummy-ns"
>
  <f:layout name="Default" />

  <f:section name="Item">
    <ul class="grid w-full gap-8 list-inside md:grid-cols-2 xl:grid-cols-4">
      <f:for each="{items}" as="item" iteration="itemIterator">
        <f:variable name="itemData" value="{ points: item.points }" />
        <f:variable
          name="itemJsonData"
          value="{itemData -> v:format.json.encode()}"
        />

        <li
          x-giby-data="{itemJsonData}"
          x-giby-bind="card"
          class="w-full rounded-lg cursor-pointer"
        >
          <gdmcomc:content.card
            header="{
                        headline: item.header,
                        layout: 106
                    }"
            icon="{item.icon}"
            class="border-0"
          />
        </li>
      </f:for>
    </ul>
  </f:section>

  <f:section name="Steps">
    <f:for each="{steps}" as="step" iteration="stepIterator">
      <f:variable name="stepData" value="{ stepIndex: stepIterator.index }" />
      <f:variable
        name="stepJsonData"
        value="{stepData -> v:format.json.encode()}"
      />

      <template x-giby-if="!isFinalStep">
        <div
          x-giby-data="{stepJsonData}"
          x-giby-bind="stepItemBinding"
          class="flex flex-col w-full gap-6"
        >
          <div class="flex items-center">
            <gdmcomc:headline headline="{step.question}" layout="105" />
            <span x-giby-show="isCurrentStepMultiselect" class="text-2xl">
              &nbsp;- Mehrere Antworten wählbar
            </span>
          </div>
          <f:render section="Item" arguments="{items: step.answers}" />
        </div>
      </template>
    </f:for>

    <f:variable name="stepLength" value="{f:count(subject: steps)}" />
    <f:variable name="resultStepData" value="{ stepIndex: stepLength }" />
    <f:variable
      name="resultStepJsonData"
      value="{resultStepData -> v:format.json.encode()}"
    />
    <template x-giby-if="isFinalStep">
      <div
        x-giby-data="{resultStepJsonData}"
        class="flex flex-col w-full gap-10"
      >
        <div class="flex flex-col gap-3">
          <gdmcomc:headline
            headline="Vielen Dank für Ihre Antworten!"
            layout="105"
            class="w-full text-primary-700"
          />
          <gdmcomc:headline
            headline="Wir empfehlen Ihnen für Ihren Bedarf folgende Bandbreite:"
            layout="105"
            class="w-full"
          />
        </div>
        <div
          x-giby-bind="suggestedProductBinding"
          class="relative p-1 overflow-hidden rounded-lg before:absolute before:content-[''] before:top-0 before:left-0 before:h-full before:w-full before:-z-[1] before:bg-conic-gradient before:rounded-lg before:animate-border-spin"
        >
          <f:for each="{products}" as="product">
            <f:cObject typoscriptObjectPath="lib.tx_mask.content">
              {product.uid}
            </f:cObject>
          </f:for>
        </div>
      </div>
    </template>
  </f:section>

  <f:section name="Wizard">
    <f:variable
      name="steps"
      value="{
        0: {
            multiselect: 0,
            question: 'Wie viele Personen nutzen zu Hause das Internet?',
            name: 'Personen',
            answers: {
                0: {
                    icon: 'default/user',
                    header: '1 Person',
                    points: 1
                },
                1: {
                    icon: 'default/users',
                    header: '2 Personen',
                    points: 2
                },
                2: {
                    icon: 'default/users-group',
                    header: '3 Personen',
                    points: 3
                },
                3: {
                    icon: 'default/users-group-+',
                    header: 'Über 3 Personen',
                    points: 4
                },
            },
        },
        1: {
            multiselect: 1,
            question: 'Wofür wird das Internet zu Hause genutzt?',
            name: 'Verwendung',
            answers: {
                0: {
                    icon: 'default/shopping-bag',
                    header: 'Surfen, Online Shopping',
                    points: 1
                },
                1: {
                    icon: 'default/video-camera',
                    header: 'Video-Streaming',
                    points: 2
                },
                2: {
                    icon: 'default/desktop-pc',
                    header: 'Home Office',
                    points: 3
                },
                3: {
                    icon: 'default/rocket',
                    header: 'Online Spiele und Gaming',
                    points: 4
                },
            },
        },
        2: {
            multiselect: 0,
            question: 'Wie viele internetfähige Endgeräte werden zu Hause genutzt?',
            name: 'Geräte',
            answers: {
                0: {
                    icon: 'default/devices',
                    header: '1-3 Geräte',
                    points: 1
                },
                1: {
                    icon: 'default/devices',
                    header: '3-6 Geräte',
                    points: 2
                },
                2: {
                    icon: 'default/devices',
                    header: '6-9 Geräte',
                    points: 3
                },
                3: {
                    icon: 'default/rocket',
                    header: 'Über 10 Geräte',
                    points: 4
                },
            },
        },
    }"
    />
    <f:variable
      name="navigationData"
      value="{
              0: {
                name: steps.0.name
              },
              1: {
                name: steps.1.name
              },
              2: {
                name: steps.2.name
              },
              3: {
                name: 'Ergebnis'
              },
            }"
    />
    <f:variable
      name="wizardModuleData"
      value="{
        steps: steps,
    }"
    />
    <f:variable
      name="baseButtonClass"
      value="inline-flex items-center gap-2 hover:underline font-bold focus-visible:outline-none focus:ring-4 focus:ring-primary-600 rounded-lg"
    />
    <f:variable
      name="primaryButtonClass"
      value="{baseButtonClass} bg-primary-500 focus:bg-primary-200 hover:bg-primary-200 ml-auto py-2.5 px-5 dark:bg-primary-300 dark:hover:bg-primary-500 dark:focus:bg-primary-500 dark:text-black"
    />
    <f:variable
      name="secondaryButtonClass"
      value="{baseButtonClass} bg-white border border-primary-500 hover:border-primary-200 hover:bg-primary-200 py-[9px] px-[19px] dark:bg-gray-800 dark:text-white dark:hover:bg-primary-500 dark:hover:text-black dark:hover:border-primary-500 dark:focus:border-secondary-100"
    />
    <f:variable
      name="disabledButtonClass"
      value="disabled:bg-gray-100 disabled:pointer-events-none disabled:text-gray-700 dark:disabled:text-gray-400 dark:disabled:bg-gray-800"
    />

    <div
      x-giby-data="wizard({wizardModuleData -> v:format.json.encode()})"
      class="flex flex-wrap gap-10"
    >
      <giby:wizard.navigation data="{navigationData}" />
      <f:render
        section="Steps"
        arguments="{ steps: steps, products: products }"
      />

      <button
        class="{secondaryButtonClass}"
        x-giby-show="currentStepIndex > 0"
        x-giby-on:click="goToPrevStep"
      >
        <gdmcomc:icon name="default/arrow-left" class="size-4" />
        Zurück
      </button>

      <button
        class="{primaryButtonClass} {disabledButtonClass}"
        x-giby-on:click="goToNextStep"
        x-giby-bind="ctaBinding"
      >
        <span>Weiter</span>
        <gdmcomc:icon name="default/arrow-right" class="size-4" />
      </button>

      <button
        class="{primaryButtonClass} {disabledButtonClass}"
        x-giby-show="isFinalStep"
        x-giby-on:click="reset"
      >
        Test wiederholen
      </button>
    </div>
  </f:section>

  <f:section name="Main">
    <f:variable
      name="products"
      value="{
                0: children_100.0,
                1: children_200.0,
                2: children_300.0,
            }"
    />

    <f:render section="Wizard" arguments="{_all}" />
  </f:section>
</html>
