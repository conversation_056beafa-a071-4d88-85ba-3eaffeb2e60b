
const Encore = require('@symfony/webpack-encore');
const SVGSpritemapPlugin = require('svg-spritemap-webpack-plugin');

// Manually configure the runtime environment if not already configured yet by the "encore" command.
// It's useful when you use tools that rely on webpack.config.js file.
if (!Encore.isRuntimeEnvironmentConfigured()) {
    Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev');
}

const extensionKey = 'giby'
const rootPath = '../../public/'
const publicPath = `/build/${extensionKey}`
const outputPath = `${rootPath}${publicPath}`

Encore
    .setOutputPath(outputPath)
    .setPublicPath(publicPath)

    /*
     * ENTRY CONFIG
     *
     * Add 1 entry for each "page" of your app
     * (including one that's included on every page - e.g. "app")
     *
     * Each entry will result in one JavaScript file (e.g. app.js)
     * and one CSS file (e.g. app.css) if your JavaScript imports CSS.
     */
    .addEntry('giby_main', './Resources/Private/TypeScript/main.ts')

    .addStyleEntry(`rte`, './Resources/Private/Css/rte.css')

    // When enabled, Webpack "splits" your files into smaller pieces for greater optimization.
    .splitEntryChunks()

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .enableSingleRuntimeChunk()
    .cleanupOutputBeforeBuild()
    .enableBuildNotifications()
    .enableSourceMaps(!Encore.isProduction())
    // enables hashed filenames (e.g. app.abc123.css)
    .enableVersioning(Encore.isProduction())

    .configureFilenames({
        js: '[name].[fullhash:8].min.js',
        css: '[name].[fullhash:8].min.css',
        // images: 'images/[name].[hash:8].[ext]',
        // fonts: 'fonts/[name].[hash:8].[ext]'
    })

    // enables @babel/preset-env polyfills
    .configureBabelPresetEnv((config) => {
        config.useBuiltIns = 'usage';
        config.corejs = 3;
    })

    .configureBabel(() => { }, {
        useBuiltIns: 'usage',
        corejs: 3
    })

    // enables PostCss support
    .enablePostCssLoader()

    // uncomment if you use TypeScript
    .enableTypeScriptLoader()

    .addPlugin(new SVGSpritemapPlugin('./Resources/Public/Assets/Logos/**/*.svg', {
        output: {
            filename: 'giby-logos-spritemap.svg',
            svgo: {
                plugins: [
                    {
                        name: 'cleanupIDs',
                        params: {
                            minify: false,
                        },
                    },
                ]
            },
        },
    }))
let config = Encore.getWebpackConfig();
config.name = extensionKey
module.exports = config;
