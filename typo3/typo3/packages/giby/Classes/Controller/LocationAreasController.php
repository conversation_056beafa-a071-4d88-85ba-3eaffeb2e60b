<?php

namespace Mogic\Giby\Controller;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;

/**
 * List locations
 */
class LocationAreasController extends ActionController
{
    /**
     * Displays services
     */
    public function mapAction(): ResponseInterface
    {
        if (!$this->settings["kml_file"]) {
            return $this->htmlResponse();
        }

        $contentUid = $this->configurationManager->getContentObject()->data['uid'];
        $fileRepository = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Resource\FileRepository::class);
        $fileObjects = $fileRepository->findByRelation("tt_content", "settings.kml_file", $contentUid);
        $fileObject = current($fileObjects);
        $fileObjectContent = $fileObject->getContents();
        $xmlFileObject = simplexml_load_string($fileObjectContent);

        $areas = [];
        $status = [];
        $features = [];
        $locationAreas = [];

        $areaFilterNames = $this->request->hasArgument('areas')
            ? array_filter((array) $this->request->getArgument('areas'))
            : [];

        $statusFilterNames = $this->request->hasArgument('status')
            ? array_filter((array) $this->request->getArgument('status'))
            : [];

        $xmlFolders = $xmlFileObject->Document[0]->Folder;
        foreach($xmlFolders as $xmlFolder) {
            $areaName = (string) $xmlFolder->name;

            if (!empty($areaFilterNames) && !in_array($areaName, $areaFilterNames)) {
                continue;
            }

            $areas[$areaName] = 0;

            foreach($xmlFolder->Folder as $nextFolder) {
                $statusLabel = (string) $nextFolder->name;

                if (!empty($statusFilterNames) && !in_array($statusLabel, $statusFilterNames)) {
                    continue;
                }

                if (!array_key_exists($statusLabel, $status)) {
                    $status[$statusLabel] = 0;
                }

                foreach($nextFolder->Placemark as $placemark) {
                    $coordinates = explode(
                        " ",
                        trim((string) $placemark->Polygon->outerBoundaryIs->LinearRing->coordinates)
                    );

                    $coordinates = [
                        array_map(
                            function($coordinateString) {
                                return array_map(
                                    "floatval",
                                    array_slice(
                                        explode(",", $coordinateString),
                                        0,
                                        2
                                    )
                                );
                            },
                            $coordinates
                        )
                    ];

                    $locationArea = [
                        "name" => (string) $placemark->name,
                        "status" => $statusLabel,
                        "area" => $areaName,
                        "established" => (string) $placemark->description,
                        "uid" => uniqid()
                    ];
                    $locationAreas[] = $locationArea;

                    $feature = [
                        "type" => "Feature",
                        "properties" => $locationArea,
                        "geometry" => [
                            "type" => "Polygon",
                            "coordinates" => $coordinates
                        ],
                    ];

                    $areas[$areaName]++;
                    $status[$statusLabel]++;
                    $features[] = $feature;
                }
            }
        }

        $geojson = [
            "type" => "FeatureCollection",
            "features" => $features
        ];

        $uniqueStatusFilter = $this->parseFilterPart(
            $status,
            true
        );

        $areaNameFilter = $this->parseFilterPart(
            $areas,
            true
        );

        $this->view->assignMultiple(
            [
                'contentUid'    => $contentUid,
                'status'        => $uniqueStatusFilter,
                'areas'         => $areaNameFilter,
                'geoJson'       => $geojson,
                'locationAreas' => $locationAreas,
                'selected'      => [
                    'areas'         => $areaFilterNames,
                    'status'        => $statusFilterNames,
                ],
            ]
        );

        return $this->htmlResponse();
    }

    protected function parseFilterPart(array $items, bool $hideEmptyValues = false): array
    {
        $filter = array_map(
            function($name, $count) {
                return [
                    "title" => $name,
                    "count" => $count,
                    "uid" => $name
                ];
            },
            array_keys($items),
            $items,
        );

        if (!$hideEmptyValues) {
            return $filter;
        }

        return array_filter($filter, fn($item) => $item["count"] > 0);
    }
}
