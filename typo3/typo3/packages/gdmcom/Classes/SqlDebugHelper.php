<?php

namespace Mogic\GdmCom;

use TYPO3\CMS\Core\Database\Query\QueryBuilder;

class SqlDebugHelper
{
    public static function getFullSql(QueryBuilder $qb): string
    {
        $sql = $qb->getSQL();
        $params = $qb->getParameters();

        foreach ($params as $key => $value) {
            $newKey = ':' . $key;
            unset($params[$key]);

            if (is_string($value)) {
                $value = '"' . $value . '"';

            } elseif (is_bool($value)) {
                $value = (int) $value;

            } elseif (is_null($value)) {
                $value = 'NULL';

            } elseif (is_array($value)) {
                $value = implode(', ', $value);
            }
            $params[$newKey] = $value;
        }

        uasort(
            $params, function ($first, $second) {
                return strlen($first) - strlen($second);
            }
        );

        return str_replace(array_keys($params), array_values($params), $sql);
    }
}
