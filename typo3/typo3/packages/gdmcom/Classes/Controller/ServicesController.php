<?php

namespace Mogic\GdmCom\Controller;

use Mogic\GdmCom\Domain\CategoryHelper;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\FrontendRestrictionContainer;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Frontend\Typolink\LinkFactory;

/**
 * List services
 */
class ServicesController extends ActionController
{
    /**
     * Displays services
     */
    public function listAction(): ResponseInterface
    {
        $companyCategoryIds = $this->settings['companies']
            ? explode(',', $this->settings['companies'])
            : [];
        $serviceCategoryId = $this->settings['serviceCategory']
            ? intval($this->settings['serviceCategory'])
            : null;

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_gdmcom_services');
        $qb->setRestrictions(
            GeneralUtility::makeInstance(FrontendRestrictionContainer::class)
        );
        $query = $qb->select(
            'uid', 'header', 'bodytext', 'link', 'icon',
            'categories'
        )
            ->from('tx_gdmcom_services')
            ->groupBy('tx_gdmcom_services.uid')
            ->orderBy('tx_gdmcom_services.header');

        CategoryHelper::addCategoryFilter(
            $query, 'mm_comp', $companyCategoryIds, 'tx_gdmcom_services'
        );
        if ($serviceCategoryId !== null) {
            CategoryHelper::addCategoryFilter(
                $query, 'mm_cat', [$serviceCategoryId], 'tx_gdmcom_services');
        }

        $serviceRecords = $query
            ->executeQuery()
            ->fetchAllAssociative();
        $this->loadServiceRecordCategories($serviceRecords);

        $companyCategories = [];
        foreach ($serviceRecords as $serviceRecord) {
            foreach ($serviceRecord['company'] as $catUid => $dummy) {
                $companyCategories[$catUid] = $serviceRecord['categories'][$catUid];
            }
        }

        uasort(
            $companyCategories,
            function ($catA, $catB) {
                return $catA['sorting'] - $catB['sorting'];
            }
        );

        $this->view->assignMultiple(
            [
                'companyCategories' => $companyCategories,
                'serviceRecords'    => $serviceRecords,
                'hideBadge'         => $serviceCategoryId !== null,
                'htmlMoreStart' => '<div class="moreservices">',
                'htmlMoreEnd'   => '</div>',
            ]
        );
        return $this->htmlResponse();
    }

    /**
     * Load categories related to the given service records and make them
     * available as properties in the records.
     *
     * Adds "company" and "serviceCategory" properties.
     */
    protected function loadServiceRecordCategories(array &$serviceRecords): void
    {
        $recordUids = array_column($serviceRecords, 'uid');

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('sys_category_record_mm');
        $categoryMmRecords = $qb->select(
            'uid_local', 'parent', 'title', 'uid_foreign', 'cat.sorting'
        )
            ->from('sys_category_record_mm')
            ->join(
                'sys_category_record_mm',
                'sys_category', 'cat',
                $qb->expr()->eq(
                    'cat.uid', $qb->quoteIdentifier('sys_category_record_mm.uid_local')
                )
            )
            ->orderBy('uid_foreign')
            ->where(
                $qb->expr()->eq('tablenames', $qb->createNamedParameter('tx_gdmcom_services')),
                $qb->expr()->eq('fieldname', $qb->createNamedParameter('categories')),
                $qb->expr()->in(
                    'uid_foreign', $qb->createNamedParameter(
                        $recordUids, Connection::PARAM_INT_ARRAY
                    )
                )
            )
            ->executeQuery()
            ->fetchAllAssociative();

        $recordCategories = [];
        foreach ($categoryMmRecords as $mmRecord) {
            $recordCategories[$mmRecord['uid_foreign']][$mmRecord['uid_local']] = [
                'uid'     => $mmRecord['uid_local'],
                'parent'  => $mmRecord['parent'],
                'title'   => $mmRecord['title'],
                'sorting' => $mmRecord['sorting'],
            ];
        }

        foreach ($serviceRecords as $key => $serviceRecord) {
            $serviceRecords[$key]['categories'] = $recordCategories[$serviceRecord['uid']] ?? [];
        }

        $parentCategoryKeys = $this->getParentCategoryKeys();
        foreach ($serviceRecords as $serviceKey => $serviceRecord) {
            foreach ($parentCategoryKeys as $propertyKey) {
                $serviceRecords[$serviceKey][$propertyKey] = [];
            }
            foreach ($serviceRecord['categories'] as $category) {
                $propertyKey = $parentCategoryKeys[$category['parent']] ?? null;
                if (!$propertyKey) {
                    continue;
                }
                $serviceRecords[$serviceKey][$propertyKey][$category['uid']] = $category['title'];
            }
            foreach ($parentCategoryKeys as $propertyKey) {
                $serviceRecords[$serviceKey][$propertyKey . 'IdStr']
                    = implode(',', array_keys($serviceRecords[$serviceKey][$propertyKey]));
                $serviceRecords[$serviceKey][$propertyKey . 'Str']
                    = implode(', ', $serviceRecords[$serviceKey][$propertyKey]);
            }
        }
    }

    /**
     * Get an array with all category properties a page record can have,
     * and the parent category uid the category must be in to belong to it.
     */
    protected function getParentCategoryKeys(): array
    {
        $categorySettings = $this->request->getAttribute('site')->getSettings()->get('categories');
        return [
            $categorySettings['jobCompany']        => 'company',
            $categorySettings['serviceCategories'] => 'serviceCategory',
        ];
    }
}
