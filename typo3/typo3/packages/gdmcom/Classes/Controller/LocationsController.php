<?php

namespace Mogic\GdmCom\Controller;

use Mogic\GdmCom\Domain\CategoryHelper;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\FrontendRestrictionContainer;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;

/**
 * List locations
 */
class LocationsController extends ActionController
{
    /**
     * Displays services
     */
    public function mapAction(): ResponseInterface
    {
        $companyCategoryIds = $this->settings['companies']
            ? explode(',', $this->settings['companies'])
            : [];

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_gdmcom_locations');
        $qb->setRestrictions(
            GeneralUtility::makeInstance(FrontendRestrictionContainer::class)
        );
        $query = $qb->select(
            'uid', 'title', 'headquarter',
            'address', 'loc_lat', 'loc_lng',
            'email', 'telephone', 'website',
            'categories'
        )
            ->from('tx_gdmcom_locations')
            ->groupBy('tx_gdmcom_locations.uid')
            ->orderBy('tx_gdmcom_locations.title');

        CategoryHelper::addCategoryFilter(
            $query, 'mm_comp', $companyCategoryIds, 'tx_gdmcom_locations'
        );

        $locationsRecords = $query
            ->executeQuery()
            ->fetchAllAssociative();

        $this->loadLocationRecordCompany($locationsRecords);

        $companyCategories = [];
        foreach ($locationsRecords as $locationsRecord) {
            $companyCategories[$locationsRecord['company']["uid"]] = $locationsRecord['company'];
        }

        uasort(
            $companyCategories,
            function ($catA, $catB) {
                return $catA['sorting'] - $catB['sorting'];
            }
        );

        $this->view->assignMultiple(
            [
                'companyCategories' => $companyCategories,
                'locationRecords'   => $locationsRecords,
                'geoJson'           => $this->transformLocationRecordsToGeoJson($locationsRecords)
            ]
        );
        return $this->htmlResponse();
    }

    /**
     * Load categories related to the given service records and make them
     * available as properties in the records.
     *
     * Adds "company" and "serviceCategory" properties.
     */
    protected function loadLocationRecordCompany(array &$locationRecords): void
    {
        $recordUids = array_column($locationRecords, 'uid');

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('sys_category_record_mm');
        $categoryMmRecords = $qb->select(
            'uid_local', 'parent', 'title', 'uid_foreign', 'cat.sorting'
        )
            ->from('sys_category_record_mm')
            ->join(
                'sys_category_record_mm',
                'sys_category', 'cat',
                $qb->expr()->eq(
                    'cat.uid', $qb->quoteIdentifier('sys_category_record_mm.uid_local')
                )
            )
            ->orderBy('uid_foreign')
            ->where(
                $qb->expr()->eq('tablenames', $qb->createNamedParameter('tx_gdmcom_locations')),
                $qb->expr()->eq('fieldname', $qb->createNamedParameter('categories')),
                $qb->expr()->in(
                    'uid_foreign', $qb->createNamedParameter(
                        $recordUids, Connection::PARAM_INT_ARRAY
                    )
                )
            )
            ->executeQuery()
            ->fetchAllAssociative();

        $recordCategories = [];
        foreach ($categoryMmRecords as $mmRecord) {
            $recordCategories[$mmRecord['uid_foreign']][$mmRecord['uid_local']] = [
                'uid'           => $mmRecord['uid_local'],
                'parent'        => $mmRecord['parent'],
                'title'         => $mmRecord['title'],
                'sorting'       => $mmRecord['sorting'],
            ];
        }

        $companyLogoDefinition = [
            "GIBY"                  => "giby",
            "GDMcom GmbH"           => "gdmcom",
            "GDMcom | Bau GmbH"     => "construction",
            "GDMcom | Planung GmbH" => "planning"
        ];

        $locationRecords = array_map(
            function ($locationRecord) use ($companyLogoDefinition, $recordCategories) {
                $categoryRecord = $recordCategories[$locationRecord["uid"]];
                $company = current($categoryRecord);
                $company["logo"] = str_replace(
                    array_keys($companyLogoDefinition),
                    array_values($companyLogoDefinition),
                    $company["title"]
                );
                $locationRecord["company"] = $company;
                return $locationRecord;
            },
            $locationRecords
        );
    }

    /**
     * Get an array with all category properties a page record can have,
     * and the parent category uid the category must be in to belong to it.
     */
    protected function getParentCategoryKeys(): array
    {
        $categorySettings = $this->request->getAttribute('site')->getSettings()->get('categories');
        return [
            $categorySettings['jobCompany']         => 'company'
        ];
    }

    /**
     * transform the location records to a geojson layer
     * https://wiki.openstreetmap.org/wiki/GeoJSON
     */
    protected function transformLocationRecordsToGeoJson(array $locationRecords): array
    {
        return [
            "type" => "FeatureCollection",
            "features" => array_map(
                function ($locationRecord) {
                    return [
                        "type"          => "Feature",
                        "properties"    => [
                            "companyId"     => $locationRecord["company"]["uid"],
                            "headquarter"   => !!$locationRecord["headquarter"],
                            "uid"           => $locationRecord["uid"]
                        ],
                        "geometry"      => [
                            "type"          => "Point",
                            "coordinates"   => [
                                $locationRecord["loc_lng"],
                                $locationRecord["loc_lat"]
                            ]
                        ]
                    ];
                },
                $locationRecords
            )
        ];
    }
}
