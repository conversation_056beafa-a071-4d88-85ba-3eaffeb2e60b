<?php

namespace Mogic\GdmCom\Controller;

use Mogic\GdmCom\Domain\Repository\JobFiltersRepository;
use Mogic\GdmCom\Domain\CategoryHelper;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\FrontendRestrictionContainer;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Frontend\Typolink\LinkFactory;

/**
 * List jobs
 */
class JobsController extends ActionController
{
    /**
     * Displays highlight jobs as cards
     */
    public function cardsAction(): ResponseInterface
    {
        $companyCategoryIds = $this->settings['companies']
            ? explode(',', $this->settings['companies'])
            : [];

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $qb->setRestrictions(
            GeneralUtility::makeInstance(FrontendRestrictionContainer::class)
        );
        $query = $qb->select(
            'uid', 'title', 'categories', 'url',
            'tx_gdmcom_jobstart', 'tx_gdmcom_highlight'
        )
            ->from('pages')
            ->groupBy('pages.uid')
            ->orderBy('pages.title')
            ->where(
                $qb->expr()->eq('doktype', $qb->createNamedParameter(30)),
                $qb->expr()->eq('tx_gdmcom_highlight', $qb->createNamedParameter(1))
            );

        CategoryHelper::addCategoryFilter($query, 'mm', $companyCategoryIds);

        $jobPageRecords = $query
            ->executeQuery()
            ->fetchAllAssociative();
        $jobPageRecords = $this->formatDates($jobPageRecords);

        $this->loadPageRecordCategories($jobPageRecords);
        $this->loadPageRecordDetailUrls($jobPageRecords);
        $this->view->assign('jobPageRecords', $jobPageRecords);
        return $this->htmlResponse();
    }

    public function listAction(): ResponseInterface
    {
        $companyCategoryIds = $this->settings['companies']
            ? explode(',', $this->settings['companies'])
            : [];

        $filterCompanyIds = $this->request->hasArgument('company')
            ? array_filter((array) $this->request->getArgument('company'))
            : [];
        $filterLocationIds = $this->request->hasArgument('location')
            ? array_filter((array) $this->request->getArgument('location'))
            : [];
        $filterCarreerIds = $this->request->hasArgument('carreer')
            ? array_filter((array) $this->request->getArgument('carreer'))
            : [];
        $startNum = $this->request->hasArgument('from')
            ? intval($this->request->getArgument('from'))
            : 0;

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $qb->setRestrictions(
            GeneralUtility::makeInstance(FrontendRestrictionContainer::class)
        );
        $query = $qb->select(
            'uid', 'title', 'categories', 'url',
            'tx_gdmcom_jobstart'
        )
            ->from('pages')
            ->groupBy('pages.uid')
            ->orderBy('pages.title')
            ->setFirstResult($startNum)
            ->setMaxResults(24)
            ->where(
                $qb->expr()->eq('doktype', $qb->createNamedParameter(30))
            );

        CategoryHelper::addCategoryFilter($query, 'mm_comp', $companyCategoryIds);
        CategoryHelper::addCategoryFilter($query, 'mm_fcomp', $filterCompanyIds);
        CategoryHelper::addCategoryFilter($query, 'mm_floc', $filterLocationIds);
        CategoryHelper::addCategoryFilter($query, 'mm_fcarr', $filterCarreerIds);

        $jobPageRecords = $query
            ->executeQuery()
            ->fetchAllAssociative();
        $jobPageRecords = $this->formatDates($jobPageRecords);

        $jobCount = $query->count('*')->executeQuery()->rowCount();

        $this->loadPageRecordCategories($jobPageRecords);
        $this->loadPageRecordDetailUrls($jobPageRecords);

        $filterRepo = new JobFiltersRepository(
            $companyCategoryIds,
            $filterCompanyIds, $filterLocationIds, $filterCarreerIds,
            $this->request
        );

        $this->view->assignMultiple(
            [
                'jobCount'       => $jobCount,
                'jobPageRecords' => $jobPageRecords,
                'filterOptions' => $filterRepo->calculateAvailableFilters(),
                'selected' => [
                    'company'  => $filterCompanyIds,
                    'location' => $filterLocationIds,
                    'carreer'  => $filterCarreerIds,
                ],
                'htmlMoreJobsStart' => '<div class="morejobs">',
                'htmlMoreJobsEnd'   => '</div>',
            ]
        );

        return $this->htmlResponse();
    }

    /**
     * Convert job start dates from YYYY-MM-DD to DD.MM.YYYY
     */
    protected function formatDates(array $jobPageRecords): array
    {
        foreach ($jobPageRecords as $key => $record) {
            $matched = preg_match(
                '#^([0-9]{4})-([0-9]{2})-([0-9]{2})$#', $record['tx_gdmcom_jobstart'],
                $matches
            );
            if ($matched) {
                $jobPageRecords[$key]['tx_gdmcom_jobstart']
                    = $matches[3] . '.' . $matches[2] . '.' . $matches[1];
            }
        }
        return $jobPageRecords;
    }

    /**
     * Get an array with all category properties a page record can have,
     * and the parent category uid the category must be in to belong to it.
     */
    protected function getParentCategoryKeys(): array
    {
        $categorySettings = $this->request->getAttribute('site')->getSettings()->get('categories');
        if ($categorySettings === null) {
            throw new \Exception('Job category site settings missing', 1741332342);
        }
        return [
            $categorySettings['jobCarreer']  => 'carreer',
            $categorySettings['jobCompany']  => 'company',
            $categorySettings['jobLocation'] => 'location',
            $categorySettings['jobModel']    => 'model',
        ];
    }

    /**
     * Load job categories related to the given job page records and make them
     * available as properties in the records.
     *
     * Adds "carreer", "company", "location" and "model" properties.
     */
    protected function loadPageRecordCategories(array &$jobPageRecords): void
    {
        $pageUids = array_column($jobPageRecords, 'uid');

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('sys_category_record_mm');
        $categoryMmRecords = $qb->select('uid_local', 'parent', 'title', 'uid_foreign')
            ->from('sys_category_record_mm')
            ->join(
                'sys_category_record_mm',
                'sys_category', 'cat',
                $qb->expr()->eq(
                    'cat.uid', $qb->quoteIdentifier('sys_category_record_mm.uid_local')
                )
            )
            ->orderBy('uid_foreign')
            ->where(
                $qb->expr()->eq('tablenames', $qb->createNamedParameter('pages')),
                $qb->expr()->eq('fieldname', $qb->createNamedParameter('categories')),
                $qb->expr()->in(
                    'uid_foreign', $qb->createNamedParameter(
                        $pageUids, Connection::PARAM_INT_ARRAY
                    )
                )
            )
            ->executeQuery()
            ->fetchAllAssociative();

        $pageCategories = [];
        foreach ($categoryMmRecords as $mmRecord) {
            $pageCategories[$mmRecord['uid_foreign']][] = [
                'uid'    => $mmRecord['uid_local'],
                'parent' => $mmRecord['parent'],
                'title'  => $mmRecord['title'],
            ];
        }

        foreach ($jobPageRecords as $key => $pageRecord) {
            $jobPageRecords[$key]['categories'] = $pageCategories[$pageRecord['uid'] ?? []];
        }

        $parentCategoryKeys = $this->getParentCategoryKeys();
        foreach ($jobPageRecords as $pageKey => $pageRecord) {
            foreach ($parentCategoryKeys as $propertyKey) {
                $jobPageRecords[$pageKey][$propertyKey] = [];
            }
            foreach ($pageRecord['categories'] as $category) {
                $propertyKey = $parentCategoryKeys[$category['parent']] ?? null;
                if (!$propertyKey) {
                    continue;
                }
                $jobPageRecords[$pageKey][$propertyKey][$category['uid']] = $category['title'];
            }
            foreach ($parentCategoryKeys as $propertyKey) {
                $jobPageRecords[$pageKey][$propertyKey . 'Str']
                    = implode(', ', $jobPageRecords[$pageKey][$propertyKey]);
            }
        }
    }

    /**
     * Add the "detailUrl" property to each page record,
     * which uses the external or internal page URL.
     */
    protected function loadPageRecordDetailUrls(array &$jobPageRecords): void
    {
        $linkFactory = GeneralUtility::makeInstance(LinkFactory::class);
        $cObj = $this->request->getAttribute('frontend.controller')->cObj;

        foreach ($jobPageRecords as $pageKey => $pageRecord) {
            if ($pageRecord['url']) {
                $jobPageRecords[$pageKey]['detailUrl']    = $pageRecord['url'];
                $jobPageRecords[$pageKey]['detailNewTab'] = true;
                continue;
            }

            $link = $linkFactory->create(
                '',
                [
                    'parameter' => $pageRecord['uid']
                ],
                $cObj
            );
            $jobPageRecords[$pageKey]['detailUrl']    = $link->getUrl();
            $jobPageRecords[$pageKey]['detailNewTab'] = strpos(
                $jobPageRecords[$pageKey]['detailUrl'],
                '://'
            ) !== false;
        }
    }
}
