<?php

namespace Mogic\GdmCom\ViewHelpers;

use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

class DynamicTagViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    protected $escapeOutput = false;

    public function initializeArguments(): void
    {
        parent::initializeArguments();

        $this->registerArgument('class', 'string', 'the class attribute');
        $this->registerArgument('as', 'string', 'the tag');
        $this->registerArgument('additionalAttributes', 'array', 'more attributes');
    }

    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ): string {
        $tagName = 'div';
        if (isset($arguments['as'])) {
            $tagName = $arguments['as'];
        }

        $classes = "";
        if (isset($arguments['class'])) {
            $classes = $arguments['class'];
        }

        $additionalAttributes = "";
        if (isset($arguments['additionalAttributes'])) {
            $additionalAttributes = implode(
                " ",
                array_map(
                    function ($key, $value) {
                        return sprintf(
                            '%s="%s"',
                            $key,
                            $value
                        );
                    },
                    array_keys($arguments['additionalAttributes']),
                    $arguments['additionalAttributes']
                )
            );
        }

        return sprintf(
            '<%s class="%s" %s>%s</%s>',
            $tagName,
            $classes,
            $additionalAttributes,
            $renderChildrenClosure(),
            $tagName
        );
    }
}
