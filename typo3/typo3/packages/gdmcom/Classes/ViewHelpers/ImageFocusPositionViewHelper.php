<?php

namespace Mogic\GdmCom\ViewHelpers;

use TYPO3<PERSON>luid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

/**
 * ViewHelper to calculate object position class based on image focus area
 */
class ImageFocusPositionViewHelper extends AbstractViewHelper
{
    /**
     * Initialize arguments
     */
    public function initializeArguments()
    {
        $this->registerArgument('focusX', 'float', 'Focus area X coordinate (0-1)', false, 0.5);
        $this->registerArgument('focusY', 'float', 'Focus area Y coordinate (0-1)', false, 0.5);
    }

    /**
     * @param array $arguments
     * @param \Closure $renderChildrenClosure
     * @param RenderingContextInterface $renderingContext
     * @return string
     */
    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ) {
        $focusValueX = $arguments['focusX'] ?? 0.5;
        $focusValueY = $arguments['focusY'] ?? 0.5;


        $objectPosition = [
            self::getPositionValueFromFocusPoint(
                $focusValueX, ["left", "right"]
            ),
            self::getPositionValueFromFocusPoint(
                $focusValueY, ["top", "bottom"]
            )
        ];

        return self::getObjectPositionFromPosition(
            $objectPosition
        );
    }

    protected static function getObjectPositionFromPosition(array $position): string
    {
        $positionSlug = implode("-", $position);
        $objectPositionConf = [
            "left-center" => "object-left",
            "left-top" => "object-left-top",
            "left-bottom" => "object-left-bottom",
            "right-center" => "object-right",
            "right-top" => "object-right-top",
            "right-bottom" => "object-right-bottom",
            "center-top" => "object-top",
            "center-bottom" => "object-bottom",
            "center-center" => "object-center"
        ];
        return $objectPositionConf[$positionSlug];
    }


    protected static function getPositionValueFromFocusPoint(float $focusValue, array $positionValues, string $defaultValue = "center"): string
    {
        if ($focusValue < 0.33) {
            return $positionValues[0];
        }

        if ($focusValue >= 0.66) {
            return $positionValues[1];
        }

        return $defaultValue;
    }
}
