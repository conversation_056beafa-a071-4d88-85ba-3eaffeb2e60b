<?php

namespace Mogic\GdmCom\Domain;

use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;

class CategoryHelper
{
    /**
     * Filter a "pages" table query by category IDs
     */
    public static function addCategoryFilter(
        QueryBuilder $qb, string $tableAlias, ?array $categoryIds, $recordTable = 'pages'
    ) {
        if ($categoryIds === null || !count($categoryIds)) {
            return;
        }

        $qb->join(
            $recordTable,
            'sys_category_record_mm', $tableAlias,
            $qb->expr()->eq(
                $tableAlias . '.uid_foreign', $qb->quoteIdentifier($recordTable . '.uid')
            )
        );
        $qb->andWhere(
            $qb->expr()->eq($tableAlias . '.tablenames', $qb->createNamedParameter($recordTable)),
            $qb->expr()->eq($tableAlias . '.fieldname', $qb->createNamedParameter('categories')),
            $qb->expr()->in(
                $tableAlias . '.uid_local', $qb->createNamedParameter(
                    $categoryIds, Connection::PARAM_INT_ARRAY
                )
            )
        );
    }
}
