<?php

namespace Mogic\GdmCom\Domain\Repository;

use Mogic\GdmCom\Domain\CategoryHelper;

use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\FrontendRestrictionContainer;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\RequestInterface;

/**
 * Helper to work with job filters
 */
class JobFiltersRepository
{
    /**
     * System categories of companies that are may be used.
     */
    protected array $allowedCompanyIds;

    /**
     * System categories of companies that jobs shall be shown for
     * (OR)
     */
    protected array $filterCompanyIds;

    /**
     * System categories of locations that jobs shall be shown for
     * (OR)
     */
    protected array $filterLocationIds;

    /**
     * System categories of carreer levels that jobs shall be shown for
     * (OR)
     */
    protected array $filterCarreerIds;

    /**
     * The current request.
     */
    protected RequestInterface $request;

    public function __construct(
        array $allowedCompanyIds,
        array $filterCompanyIds, array $filterLocationIds, array $filterCarreerIds,
        RequestInterface $request
    ) {
        $this->allowedCompanyIds = $allowedCompanyIds;

        $this->filterCompanyIds  = $filterCompanyIds;
        $this->filterLocationIds = $filterLocationIds;
        $this->filterCarreerIds  = $filterCarreerIds;

        $this->request = $request;
    }

    public function calculateAvailableFilters()
    {
        $parentCategoryKeys = $this->getParentCategoryKeys();

        $categoriesCompany  = $this->getAvailableCategories(
            $parentCategoryKeys['company'], $this->allowedCompanyIds
        );
        $categoriesLocation = $this->getAvailableCategories($parentCategoryKeys['location']);
        $categoriesCarreer  = $this->getAvailableCategories($parentCategoryKeys['carreer']);

        foreach ($categoriesCompany as $category) {
            $category->count = $this->getJobCount(
                $category, $this->allowedCompanyIds,
                null, $this->filterLocationIds, $this->filterCarreerIds
            );
        }
        foreach ($categoriesLocation as $category) {
            $category->count = $this->getJobCount(
                $category, $this->allowedCompanyIds,
                $this->filterCompanyIds, null, $this->filterCarreerIds
            );
        }
        foreach ($categoriesCarreer as $category) {
            $category->count = $this->getJobCount(
                $category, $this->allowedCompanyIds,
                $this->filterCompanyIds, $this->filterLocationIds, null
            );
        }

        return [
            'company'  => $categoriesCompany,
            'location' => $categoriesLocation,
            'carreer'  => $categoriesCarreer,
        ];
    }

    protected function getAvailableCategories(int $parentCategory, array $allowedIds = []): array
    {
        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('sys_category');
        $qb->select('uid', 'title')
            ->from('sys_category')
            ->orderBy('sorting')
            ->where(
                $qb->expr()->eq('parent', $qb->createNamedParameter($parentCategory)),
            );
        if (count($allowedIds) > 0) {
            $qb->andWhere(
                $qb->expr()->in(
                    'uid', $qb->createNamedParameter(
                        $allowedIds, Connection::PARAM_INT_ARRAY
                    )
                )
            );
        }

        $categoryRecords = $qb->executeQuery()->fetchAllAssociative();
        $categories = [];
        foreach ($categoryRecords as $record) {
            $categories[] = (object) $record;
        }
        return $categories;
    }

    protected function getJobCount(
        object $category, array $allowedCompanyIds,
        ?array $filterCompanyIds, ?array $filterLocationIds, ?array $filterCarreerIds
    ) {
        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $qb->setRestrictions(
            GeneralUtility::makeInstance(FrontendRestrictionContainer::class)
        );
        $qb->count('*')
            ->from('pages')
            ->groupBy('uid')
            ->where(
                $qb->expr()->eq('doktype', $qb->createNamedParameter(30))
            );

        CategoryHelper::addCategoryFilter($qb, 'mm_target',  [$category->uid]);
        CategoryHelper::addCategoryFilter($qb, 'mm_comp',  $allowedCompanyIds);
        CategoryHelper::addCategoryFilter($qb, 'mm_fcomp', $filterCompanyIds);
        CategoryHelper::addCategoryFilter($qb, 'mm_floc',  $filterLocationIds);
        CategoryHelper::addCategoryFilter($qb, 'mm_fcarr', $filterCarreerIds);

        //echo \Mogic\GdmCom\SqlDebugHelper::getSql($qb) . "<br/>\n";

        return $qb->executeQuery()->rowCount();
    }

    /**
     * Get an array with all category properties a page record can have,
     * and the parent category uid the category must be in to belong to it.
     */
    protected function getParentCategoryKeys(): array
    {
        $categorySettings = $this->request->getAttribute('site')->getSettings()->get('categories');
        return [
            'carreer'  => $categorySettings['jobCarreer'],
            'company'  => $categorySettings['jobCompany'],
            'location' => $categorySettings['jobLocation'],
            'model'    => $categorySettings['jobModel'],
        ];
    }
}
