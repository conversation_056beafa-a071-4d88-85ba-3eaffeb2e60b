{"name": "mogic/gdmcom", "type": "typo3-cms-extension", "description": "GDMcom", "version": "1.0.0", "license": "GPL-2.0-or-later", "require": {"typo3/cms-core": "^12.0"}, "autoload": {"psr-4": {"Mogic\\GdmCom\\": "Classes/"}}, "extra": {"typo3/cms": {"extension-key": "gdmcom"}}, "require-dev": {"phpunit/phpunit": "^10.5"}, "config": {"allow-plugins": {"typo3/cms-composer-installers": true, "typo3/class-alias-loader": true}}}