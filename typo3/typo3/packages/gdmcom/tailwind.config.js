const safelist = require('./tailwind/safelist')
const plugins = require('./tailwind/plugins')
const extendedColors = require('./tailwind/colors')
/** @type {import('tailwindcss').Config} */
module.exports = {
  presets: [
    require('./tailwind/presets/base.config')
  ],
  content: [
    "./Resources/Private/Layouts/**/*.html",
    "./Resources/Private/Partials/**/*.html",
    "./Resources/Private/Components/**/*.html",
    "./Resources/Private/Templates/**/*.html",
  ],
  darkMode: ['selector', '[data-mode="dark"]'],
  theme: {
    extend: {
      colors: {
        ...extendedColors
      }
    }
  },
  safelist,
  plugins
};
