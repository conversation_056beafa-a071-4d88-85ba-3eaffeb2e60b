module.exports = {
  theme: {
    extend: {
      screens: {
        sm: "320px",
        md: "640px",
        lg: "768px",
        xl: "1024px",
        "2xl": "1280px",
      },
      containers: {
        sm: "320px",
        md: "640px",
        lg: "768px",
        xl: "1024px",
        "2xl": "1280px",
      },
      fontFamily: {
        sans: ["Asap", "Helvetica", "Arial", "sans-serif"],
      },
      borderWidth: {
        1: "1px",
        3: "3px",
      },
      ringWidth: {
        3: "box-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color)",
      },
      padding: {
        2.5: "10px",
      },
      boxShadow: {
        "inner-top-md": [
          "inset 0 4px 3px rgb(0 0 0 / 0.07)",
          "inset 0 2px 2px rgb(0 0 0 / 0.06)",
        ],
        sm: "0px 1px 2px 0px rgba(0, 0, 0, 0.08)",
        md: "0px 2px 4px -2px rgba(0, 0, 0, 0.05), 0px 2px 8px -1px rgba(0, 0, 0, 0.10)",
        lg: "0px 4px 9px 0px rgba(0, 0, 0, 0.08), 0px 3px 15px -3px rgba(0, 0, 0, 0.10)",
        xl: "0px 10px 25px -3px rgba(0, 0, 0, 0.04), 0px 10px 25px -3px rgba(0, 0, 0, 0.10)",
      },
      maxWidth: {
        container: "883px",
      },
      height: {
        '1em': '1em'
      }
    },
  },
}