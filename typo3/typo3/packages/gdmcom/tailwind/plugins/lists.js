const tailwindPlugin = require('tailwindcss/plugin');
const {
  getInlineIconElementStyles,
  getInlineIconStyles
} = require('./icons');

const baseListingPlugin = tailwindPlugin(function ({ addComponents, theme, addBase }) {
  addBase({
    'ul:not([class])': {
      listStyleType: 'disc',
      marginLeft: theme("spacing.5"),
      "& > li": {
        marginBottom: theme("spacing.3"),
        paddingLeft: theme("spacing.2"),
      }
    }, // Apply disc bullets to unordered lists by default
    'ol:not([class])': {
      listStyleType: 'decimal',
      marginLeft: theme("spacing.5"),
      "& > li": {
        marginBottom: theme("spacing.3"),
        paddingLeft: theme("spacing.2"),
        "&::marker": {
          fontWeight: "bold"
        }
      }
    }, // Apply decimal numbers to ordered lists by default
    'ol[class~="list-aligned"]': {
      listStyleType: 'decimal',
    },
    'ul[class~="list-aligned"]': {
      listStyleType: 'disc',
    }
  });
});

const createListStyles = (theme, color, icon) => {
  return {
    listStyleType: "none",
    '& > li': {
      lineHeight: theme("lineHeight.6"),
      paddingBottom: theme("spacing.3"),

      ...getInlineIconElementStyles({
        fontWeight: "normal"
      }),
      '&:before': {
        ...getInlineIconStyles({
          icon,
          width: theme("spacing.6"),
          height: theme("spacing.6"),
        }),
      },
    }
  }
}

const listingPlugin = tailwindPlugin(function ({ addComponents, addUtilities, theme, addBase }) {
  addComponents({
    '.list-check': createListStyles(theme, "#f7a600", "check"),
    '.list-check-circle': createListStyles(theme, "#000000", "check-circle"),
  })

  addUtilities({
    '.list-aligned': {
      display: "table",
    },
    '.list-centered': {
      margin: "0 auto",
    },
    '.list-right': {
      margin: "0 0 0 auto",
    },
  })
});

module.exports = {
    baseListingPlugin,
    listingPlugin,
    createListStyles
};
