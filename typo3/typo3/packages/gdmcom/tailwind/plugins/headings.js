const tailwindPlugin = require('tailwindcss/plugin');

// Define heading options with mobile first approach.
const headingOptions = {
    'heading-type-1': {
        'default': ['font-size: 2.25rem', 'line-height: 1.5', 'font-weight: 700'],
        'lg': ['font-size: 3rem', 'line-height: 1.5', 'font-weight: 700'],

    },
    'heading-type-2': {
        'default': ['font-size: 2.25rem', 'line-height: 1.5', 'font-weight: 200'],
        'lg': ['font-size: 3rem', 'line-height: 1.5', 'font-weight: 200'],

    },
    'heading-type-3': {
        'default': ['font-size: 1.875rem', 'line-height: 1.5', 'font-weight: 700'],
        'lg': ['font-size: 2.25rem', 'line-height: 1.5', 'font-weight: 700'],

    },
    'heading-type-4': {
        'default': ['font-size: 1.5rem', 'line-height: 1.5', 'font-weight: 700'],
        'lg': ['font-size: 1.875rem', 'line-height: 1.5', 'font-weight: 700'],

    },
    'heading-type-5': {
        'default': ['font-size: 1.25rem', 'line-height: 1.5', 'font-weight: 700'],
        'lg': ['font-size: 1.5rem', 'line-height: 1.5', 'font-weight: 700'],

    },
    'heading-type-6': {
        'default': ['font-size: 1.25rem', 'line-height: 1.5', 'font-weight: 700'],
        'lg': ['font-size: 1.25rem', 'line-height: 1.5', 'font-weight: 700'],

    },
    'heading-type-7': {
        'default': ['font-size: 1.125rem', 'line-height: 1.5', 'font-weight: 700'],
        'lg': ['font-size: 1.125rem', 'line-height: 1.5', 'font-weight: 700'],

    },
    'heading-type-hero': {
      'default': ['font-size: 2.25rem', 'line-height: 1.5', 'font-weight: 200'],
      'sm': ['font-size: 2.25rem', 'line-height: 1.5', 'font-weight: 200'],
      'md': ['font-size: 2.25rem', 'line-height: 1.5', 'font-weight: 200'],
      'lg': ['font-size: 3rem', 'line-height: 1.5', 'font-weight: 200'],
      'xl': ['font-size: 3.75rem', 'line-height: 1.5', 'font-weight: 200'],
    }
};

const plugin = tailwindPlugin(function ({ addComponents, theme }) {
  const components = {};

  // Loop through each key in the headingOptions object
  Object.keys(headingOptions).forEach((key) => {
    const stylesByBreakpoint = headingOptions[key];

    // Handle the default styles first
    const defaultStyles = stylesByBreakpoint['default']?.reduce((acc, style) => {
      const [property, value] = style.split(':').map(s => s.trim());
      acc[property] = value;
      return acc;
    }, {});

    if (defaultStyles) {
      components[`.${key}`] = defaultStyles;
    }
  });

  // Handle the responsive styles
  Object.keys(headingOptions).forEach((key) => {
      const stylesByBreakpoint = headingOptions[key];

      Object.keys(stylesByBreakpoint).forEach((breakpoint) => {
        if (breakpoint !== 'default') {
          const responsiveStyles = stylesByBreakpoint[breakpoint]?.reduce((acc, style) => {
              const [property, value] = style.split(':').map(s => s.trim());
              acc[property] = value;
              return acc;
          }, {});

          const mediaQuery = `@media (min-width: ${theme(`screens.${breakpoint}`)})`;
          if (responsiveStyles) {
            components[mediaQuery] = {
                ...components[mediaQuery],
                [`.${key}`]: responsiveStyles,
            };
          }
        }
      });
  });

  // Add the generated components to Tailwind
  addComponents(components);
});

module.exports = {
    plugin
};
