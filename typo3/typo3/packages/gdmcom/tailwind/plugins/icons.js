const getColorSpecStringForSvg = (color, fillMode) => fillMode
  ? `fill=${color}, stroke=none`
  : `fill=none, stroke=${color}`;

const getBackgroundImage = (settings) => {
  const {
    icon,
    package,
    color,
    fill
  } = {
    package: "default",
    fill: false,
    color: "#000",
    ...settings
  }

  const colorSpecString = getColorSpecStringForSvg(color, fill)
  const iconPath = `${package}/${icon}.svg`
  return `svg-load("../../Public/Assets/Icons/${iconPath}", ${colorSpecString})`
}

const getInlineIconElementStyles = (settings) => {
  const {
    display,
    columnGap,
    fontWeight
  } = {
    fontWeight: "normal",
    display: "flex",
    columnGap: "0.5rem",
    ...settings
  }

  return {
    display,
    columnGap,
    fontWeight
  }
}

const getInlineIconStyles = (settings) => {
  settings = {
    package: "default",
    fill: false,
    color: "#000",
    width: '24px',
    height: '24px',
    ...settings
  }
  const {
    width,
    height
  } = settings

  return {
    width,
    height,
    content: "''",
    flex: 'none',
    backgroundSize: "contain",
    backgroundRepeat: "no-repeat",
    ...getInlineIconBackroundImageStyle(settings)
  }
}

const getInlineIconBackroundImageStyle = (settings) => {
  return {
    backgroundImage: getBackgroundImage(settings)
  }
}

module.exports = {
    getInlineIconElementStyles,
    getInlineIconStyles,
    getInlineIconBackroundImageStyle
};
