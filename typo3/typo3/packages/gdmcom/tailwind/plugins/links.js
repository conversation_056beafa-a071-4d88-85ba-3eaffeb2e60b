const tailwindPlugin = require('tailwindcss/plugin');
const {
  getInlineIconBackroundImageStyle,
  getInlineIconElementStyles,
  getInlineIconStyles
} = require('./icons');

const createIconLinkStyle = (icon) => {
  return {
    alignItems: "center",
    ...getInlineIconElementStyles({
      display: "inline-flex",
      fontWeight: "bold"
    }),
    '&:before': {
      ...getInlineIconStyles({
        icon,
        width: "1em",
        height: "1em",
      }),
    },
    '&:hover': {
      color: "#595959",
      '&:before': {
        ...getInlineIconBackroundImageStyle({
          color: "#595959",
          icon
        })
      }
    }
  }
}

const plugin = tailwindPlugin(function ({ addComponents }) {
  addComponents({
    '.inline-link': createIconLinkStyle("globe"),
    '.inline-link-file': createIconLinkStyle("arrow-down-to-bracket"),
    '.inline-link-phone': createIconLinkStyle("phone"),
    '.inline-link-email': createIconLinkStyle("mail")
  })
});

module.exports = {
    plugin,
    createIconLinkStyle
};
