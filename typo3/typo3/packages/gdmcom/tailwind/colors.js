const generatedColorBlockByColorName = (colorName) =>
  Object.fromEntries(
    ["50", "100", "200", "300", "400", "500", "600", "700", "800", "900"].map(
      (step) => [step, `var(--${colorName}-${step})`]
    )
  );

const colors = {
  white: "#ffffff",
  black: "#000000",

  primary: generatedColorBlockByColorName("primary"),
  secondary: generatedColorBlockByColorName("secondary"),
  yellow: generatedColorBlockByColorName("yellow"),
  orange: generatedColorBlockByColorName("orange"),
  terquoise: generatedColorBlockByColorName("terquoise"),
  "dark-blue": generatedColorBlockByColorName("dark-blue"),
  gray: generatedColorBlockByColorName("gray"),
  red: generatedColorBlockByColorName("red"),
  green: generatedColorBlockByColorName("green"),
};

module.exports = colors;
