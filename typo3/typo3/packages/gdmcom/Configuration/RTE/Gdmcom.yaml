# Load default processing options
imports:
    - { resource: 'EXT:rte_ckeditor/Configuration/RTE/Processing.yaml' }
    - { resource: 'EXT:rte_ckeditor/Configuration/RTE/Editor/Base.yaml' }
    - { resource: 'EXT:rte_ckeditor/Configuration/RTE/Editor/Plugins.yaml' }
    - { resource: 'EXT:gdmcom/Configuration/RTE/LinkClasses.yaml' }
    # - { resource: 'EXT:rte_ckeditor/Configuration/RTE/Editor/LinkBrowser.yaml' }
# Additional optional TYPO3 specific configuration is available via ./Editor/LinkBrowser.yaml
# See https://docs.typo3.org/c/typo3/cms-rte-ckeditor/main/en-us/Configuration/Reference.html

# Add configuration for the editor
# For complete documentation see https://ckeditor.com/docs/ckeditor5/latest/features/index.html
editor:
  config:
    # Include custom CSS
    contentsCss: "typo3_encore:gdmcom:rte"
    toolbar:
      items:
        - style
        - heading
        # grouping separator
        - '|'
        - bold
        - italic
        - underline
        - '|'
        - link
        - '|'
        - subscript
        - superscript
        - softhyphen
        - '|'
        - bulletedList
        - numberedList
        - blockQuote
        - alignment
        - '|'
        - findAndReplace
        - link
        - '|'
        - removeFormat
        - undo
        - redo
        - '|'
        - insertTable
        - '|'
        - specialCharacters
        - horizontalLine
        - sourceEditing


    heading:
      options:
        - { model: 'paragraph', title: 'Paragraph' }
        - { model: 'heading1', view: { name: 'h1', classes: 'heading-type-1' }, title: 'Heading 1' }
        - { model: 'heading3', view: { name: 'h2', classes: 'heading-type-3' }, title: 'Heading 2' }
        - { model: 'heading4', view: { name: 'h3', classes: 'heading-type-4' }, title: 'Heading 3' }
        - { model: 'heading5', view: { name: 'h4', classes: 'heading-type-5' }, title: 'Heading 4' }
        - { model: 'heading6', view: { name: 'h5', classes: 'heading-type-6' }, title: 'Heading 5' }
        - { model: 'heading7', view: { name: 'h6', classes: 'heading-type-7' }, title: 'Heading 6' }

    style:
      definitions:
        - { name: "Check List", element: "ul", classes: [ 'list-check' ] }
        - { name: "Circle Check List", element: "ul", classes: [ 'list-check-circle' ] }
        - { name: "Liste zentriert", element: "ul", classes: [ 'list-aligned', 'list-centered' ] }
        - { name: "Liste rechtsbündig", element: "ul", classes: [ 'list-aligned', 'list-right' ] }

    alignment:
      options:
        - { name: 'left', className: 'text-start' }
        - { name: 'center', className: 'text-center' }
        - { name: 'right', className: 'text-end' }
        - { name: 'justify', className: 'text-justify' }

    list:
      properties:
        styles: true
        startIndex: true
        reversed: true

    table:
      defaultHeadings: { rows: 1 }
      contentToolbar:
        - tableColumn
        - tableRow
        - mergeTableCells
        - tableProperties
        - tableCellProperties
        - toggleTableCaption



