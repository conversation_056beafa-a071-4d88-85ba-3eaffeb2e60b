<?php

defined('TYPO3') or die();

// Define all possible aspect ratios for reuse
$allAspectRatios = [
    'default' => [
        'title' => 'Standard (4:3)',
        'value' => 4 / 3
    ],
    'standard-portrait' => [
        'title' => 'Standard/Portrait (3:4)',
        'value' => 3 / 4
    ],
    'landscape' => [
        'title' => 'Landscape (2:1)',
        'value' => 2 / 1
    ],
    'landscape-portrait' => [
        'title' => 'Landscape/Portrait (1:2)',
        'value' => 1 / 2
    ],
    'widescreen' => [
        'title' => 'Widescreen (16:9)',
        'value' => 16 / 9
    ],
    'widescreen-portrait' => [
        'title' => 'Widescreen/Portrait (9:16)',
        'value' => 9 / 16
    ],
    'free' => [
        'title' => 'Frei',
        'value' => 0.0
    ],
];

$defaultFocusArea = [
    'x' => 1 / 3,
    'y' => 1 / 3,
    'width' => 1 / 3,
    'height' => 1 / 3,
];

$defaultCoverAreas = [
    [
        'x' => 0.05,
        'y' => 0.85,
        'width' => 0.9,
        'height' => 0.1,
    ]
];

$GLOBALS['TCA']['tt_content']['types']['mask_hero']['columnsOverrides']['assets']['config']['overrideChildTca'] = [
    'columns' => [
        'crop' => [
            'config' => [
                'cropVariants' => [
                    'default' => [
                        'title' => 'GDMCom',
                        'allowedAspectRatios' => [
                            'widescreen' => $allAspectRatios['widescreen'],
                        ],
                        'focusArea' => $defaultFocusArea,
                        'coverAreas' => $defaultCoverAreas,
                    ],
                ],
            ],
        ],
    ],
];

// Content Box - Only 4:3 aspect ratio
$GLOBALS['TCA']['tt_content']['types']['mask_content_box']['columnsOverrides']['assets']['config']['overrideChildTca'] = [
    'columns' => [
        'crop' => [
            'config' => [
                'cropVariants' => [
                    'default' => [
                        'title' => 'GDMCom',
                        'allowedAspectRatios' => [
                            'default' => $allAspectRatios['default'],
                        ],
                        'focusArea' => $defaultFocusArea,
                        'coverAreas' => $defaultCoverAreas,
                    ],
                ],
            ],
        ],
    ],
];

// Card - Only widescreen aspect ratio
$GLOBALS['TCA']['tt_content']['types']['mask_card']['columnsOverrides']['assets']['config']['overrideChildTca'] = [
    'columns' => [
        'crop' => [
            'config' => [
                'cropVariants' => [
                    'default' => [
                        'title' => 'GDMCom',
                        'allowedAspectRatios' => [
                            'widescreen' => $allAspectRatios['widescreen'],
                        ],
                        'focusArea' => $defaultFocusArea,
                        'coverAreas' => $defaultCoverAreas,
                    ],
                ],
            ],
        ],
    ],
];

// Video thumbnail
call_user_func(function ($table = 'sys_file_reference') {
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns($table, [
        'poster' => [
            'exclude' => true,
            'label' => 'Thumbnail',
            'displayCond' => 'USER:Mogic\\GdmCom\\Resource\\DisplayCondition\\PosterDisplayCondition->displayPoster',
            'config' => [
                'behaviour' => [
                    'allowLanguageSynchronization' => true,
                ],
                'type' => 'link',
                'allowedTypes' => ['file'],
                'maxitems' => 1,
                'allowed' => 'common-image-types',
                'overrideChildTca' => [
                    'types' => [
                        \TYPO3\CMS\Core\Resource\AbstractFile::FILETYPE_IMAGE => [
                            'showitem' => '
                                    --palette--;;imageoverlayPalette,
                                    --palette--;;filePalette',
                        ],
                    ],
                    'columns' => [
                        'link' => false,
                        'description' => false,
                        'alternative' => false,
                        'title' => false,
                    ],
                ],
            ],
        ],
    ]);

    $GLOBALS['TCA'][$table]['palettes']['videoOverlayPalette']['showitem'] = 'title,description,--linebreak--,poster';
});
