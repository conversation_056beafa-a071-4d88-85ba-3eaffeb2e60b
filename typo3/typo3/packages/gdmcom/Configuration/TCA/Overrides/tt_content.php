<?php

defined('TYPO3') or die();

// TYPO3_CONF_VARS
call_user_func(static function () {
    // MASK
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['features']['overrideSharedFields'] =
    true;


    $GLOBALS['TCA']['tt_content']['columns']['tx_mask_button_theme']['config']['items'] = [
        [
            "label" => "Primärthema",
            "value" => "primary",
        ],
        [
            "label" => "Sekundärthema",
            "value" => "secondary",
        ]
    ];
});

// CONTAINER
call_user_func(static function () {
    $additionalFields = [
        "tx_gdmcom_container_background_color" => [
            'exclude' => 1,
            'label' => 'Hintergrundfarbe des Containers',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'default' => 'base',
                'items' => [
                    [
                        "label" => "Standard (Weiss)",
                        "value" => "base",
                        "icon"  => "EXT:gdmcom/Resources/Public/Icons/Theme/Color/base.svg"
                      ],
                      [
                        "label" => "Helles Grau (gray-50)",
                        "value" => "light-gray",
                        "icon"  => "EXT:gdmcom/Resources/Public/Icons/Theme/Color/light-gray.svg"
                      ],
                      [
                        "label" => "Helle Unternehmensfarbe (primary-50)",
                        "value" => "primary-light",
                        "icon"  => "EXT:gdmcom/Resources/Public/Icons/Theme/Color/primary-light.svg"
                      ],
                      [
                        "label" => "Unternehmensfarbe (primary-500)",
                        "value" => "primary",
                        "icon"  => "EXT:gdmcom/Resources/Public/Icons/Theme/Color/primary.svg"
                      ],
                ]
            ],
        ],
        "tx_gdmcom_container_spacing_top" => [
            'exclude' => 1,
            'label' => 'Abstand über dem Containern',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'default' => 'lg',
                'items' => [
                    [
                        'label' => 'Groß (lg)',
                        'value' => 'lg'
                    ],
                    [
                        'label' => 'Standard (base)',
                        'value' => 'base'
                    ],
                    [
                        'label' => 'Klein (sm)',
                        'value' => 'sm'
                    ],
                    [
                        'label' => 'Kein Abstand',
                        'value' => 'no_spacing'
                    ],
                ],
            ],
        ],
        "tx_gdmcom_container_spacing_bottom" => [
            'exclude' => 1,
            'label' => 'Abstand unter dem Containern',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'default' => 'lg',
                'items' => [
                    [
                        'label' => 'Groß (lg)',
                        'value' => 'lg'
                    ],
                    [
                        'label' => 'Standard (base)',
                        'value' => 'base'
                    ],
                    [
                        'label' => 'Klein (sm)',
                        'value' => 'sm'
                    ],
                    [
                        'label' => 'Kein Abstand',
                        'value' => 'no_spacing'
                    ],
                ],
            ],
        ],
        "tx_gdmcom_container_content_centered" => [
            'exclude' => 1,
            'label' => 'Container Inhalt zentriert',
            'description' => 'Soll der Inhalt des Containers zentriert dargestellt werden',
            'config' => [
                'type' => 'check',
                'behaviour' => [
                    'allowLanguageSynchronization' => true,
                ],
            ],
        ],
    ];

    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns(
        'tt_content',
        $additionalFields
    );

    $containerConfigurations = [
        'tx_gdmcom_container_one_column' => [
            1,
            ['Container (1 Spalte)', 'Standard Container mit einer Inhaltsspalte'],
            [
                "tx_gdmcom_container_content_centered"
            ]
        ],
        'tx_gdmcom_container_two_columns' => [
            2,
            ['Container (2 Spalte je 50%)', 'Standard Container mit 2 Inhaltsspalten (50%)', 2],
            []
        ],
        'tx_gdmcom_container_three_columns' => [
            3,
            ['Container (3 Spalten je 33%])', 'Standard Container mit 3 Inhaltsspalten (33%)'],
            []
        ],
        'tx_gdmcom_container_four_columns' => [
            4,
            ['Container (4 Spalten je 25%])', 'Standard Container mit 4 Inhaltsspalten (25%)'],
            [
            ]
        ]
    ];

    $standardFieldTypesForContainer = [
        'CType',
        'header',
        'tx_gdmcom_container_background_color',
        'tx_gdmcom_container_spacing_top',
        'tx_gdmcom_container_spacing_bottom'
    ];

    $containerConfigurationKeys = array_keys($containerConfigurations);

    $standardColumnConf = [
        'name' => 'Spalteninhalt ',
        'label' => "Test",
        'colPos' => 100,
        'disallowed' => [
            'CType' => implode(",", $containerConfigurationKeys)
        ]
    ];

    $containerConfigurations = array_map(
        function ($key, $conf) use ($standardColumnConf, $standardFieldTypesForContainer) {
            $columnsConf = array_fill(0, $conf[0], $standardColumnConf);
            $columns = array_map(
                function ($index, $column) {
                    $column['name'] .= " #" . $index + 1;
                    $column['colPos'] = $column['colPos'] + $index + 1;
                    return $column;
                },
                array_keys($columnsConf),
                $columnsConf
            );
            return (object) [
                "key" => $key,
                "icon" => str_replace("tx_gdmcom_", "", $key),
                "label" => $conf[1][0],
                "description" => $conf[1][1],
                "grid" => [
                    [
                        [
                            'name' => 'Inhalt des Kopfbereichs',
                            'colPos' => $standardColumnConf["colPos"],
                            'colspan' => count($columns),
                            'allowed' => [
                                'CType' => 'mask_content_snippet'
                            ],
                            'maxitems' => 1
                        ]
                    ],
                    $columns
                ],
                'showitems' => array_merge($standardFieldTypesForContainer, $conf[2])
            ];
        },
        $containerConfigurationKeys,
        $containerConfigurations
    );

    $containerRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(
        \B13\Container\Tca\Registry::class
    );

    foreach ($containerConfigurations as $container) {
        $containerRegistry->configureContainer(
            (
                new \B13\Container\Tca\ContainerConfiguration(
                    $container->key,
                    $container->label,
                    $container->description,
                    $container->grid
                )
            )->setIcon(
                "EXT:gdmcom/Resources/Public/Icons/Theme/Container/{$container->icon}.svg"
            )->setSaveAndCloseInNewContentElementWizard(true)
        );

        $GLOBALS['TCA']['tt_content']['types'][$container->key]['showitem'] = implode(",", $container->showitems);
    }
});
