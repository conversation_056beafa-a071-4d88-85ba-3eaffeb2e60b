<?php

defined('TYPO3') or die();

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'pages',
    'doktype',
    [
        'label' => 'Job',
        'value' => 30,
        'icon'  => 'apps-pagetree-page-frontend-user',
        'group' => 'default',
    ],
);
$GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][30] = 'apps-pagetree-page-frontend-user';

$GLOBALS['TCA']['pages']['columns']['tsconfig_includes']['config']['items'][] = [
    'Seiteneinstellungen Ordner "Leistungen"',
    'EXT:gdmcom/Configuration/TsConfig/Page/Specific/Services.tsconfig'
];

$GLOBALS['TCA']['pages']['columns']['tsconfig_includes']['config']['items'][] = [
    'Seiteneinstellungen Ordner "Standorte"',
    'EXT:gdmcom/Configuration/TsConfig/Page/Specific/Locations.tsconfig'
];

//custom job fields
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns(
    'pages',
    [
        'tx_gdmcom_highlight' => [
            'exclude' => 0,
            'label' => 'Highlight-Job',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
            ],
        ],
        'tx_gdmcom_jobstart' => [
            'exclude' => 0,
            'label' => 'Arbeitsbeginn',
            'description' => '"ab sofort" oder Datum "2024-12-23"',
            'config' => [
                'type' => 'input',
                'max' => 32,
                'required' => true,
            ],
        ],
    ]
);

$GLOBALS['TCA']['pages']['types'][30] = $GLOBALS['TCA']['pages']['types'][1];
$GLOBALS['TCA']['pages']['types'][30]['showitem'] = str_replace(
    [
        ' --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:categories,',
        ' categories,'
    ],
    '',
    $GLOBALS['TCA']['pages']['types'][30]['showitem']
);
$GLOBALS['TCA']['pages']['types'][30]['columnsOverrides'] = [
    'categories' => [
        'config' => [
            'size' => 35,
            'treeConfig' => [
                'startingPoints' => '###SITE:settings.categories.jobRoot###',
                'appearance' => [
                    'nonSelectableLevels' => '0,1',
                ],
            ]
        ],
    ],
    'url' => [
        'config' => [
            'required' => false
        ]
    ],
];

$GLOBALS['TCA']['pages']['palettes']['tx_gdmcom_jobspal'] = [
    'showitem' => 'tx_gdmcom_highlight, --linebreak--'
        . ', tx_gdmcom_jobstart, --linebreak--'
        . ', categories, --linebreak--'
        . ', url;URL externe Detailseite'
];
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
    'pages',
    '--div--;Jobdetails, --palette--;;tx_gdmcom_jobspal',
    '30',
    'after:--palette--;;title'
);
