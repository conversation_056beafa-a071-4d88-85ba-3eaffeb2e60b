<?php

use TYPO3\CMS\Extbase\Utility\ExtensionUtility;

defined('TYPO3') or die();

//Backend plugin registration in tt_content
ExtensionUtility::registerPlugin(
    'gdmcom',
    'JobCards',
    'Jobs: Karten',
    'content-user'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['gdmcom_jobcards']
    = 'pi_flexform';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['gdmcom_jobcards']
    = 'pages,recursive';
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'gdmcom_jobcards',
    'FILE:EXT:gdmcom/Configuration/FlexForms/JobCards.xml'
);

ExtensionUtility::registerPlugin(
    'gdmcom',
    'JobList',
    'Jobs: Liste',
    'content-user'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['gdmcom_joblist']
    = 'pi_flexform';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['gdmcom_joblist']
    = 'pages,recursive';
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'gdmcom_joblist',
    'FILE:EXT:gdmcom/Configuration/FlexForms/JobList.xml'
);

ExtensionUtility::registerPlugin(
    'gdmcom',
    'ServiceList',
    'Leistungsliste',
    'content-user'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['gdmcom_servicelist']
    = 'pi_flexform';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['gdmcom_servicelist']
    = 'pages,recursive';
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'gdmcom_servicelist',
    'FILE:EXT:gdmcom/Configuration/FlexForms/ServiceList.xml'
);



ExtensionUtility::registerPlugin(
    'gdmcom',
    'Locations',
    'Standorte',
    'content-user'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['gdmcom_locations']
    = 'pi_flexform';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['gdmcom_locations']
    = 'pages,recursive';
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'gdmcom_locations',
    'FILE:EXT:gdmcom/Configuration/FlexForms/Locations.xml'
);
