<?php

//'services/processing.svg' => 'tx-gdmcom-services-processing'
$typeiconClasses = [
    'default' => 'tx-gdmcom-services-processing',
];

$iconDir = __DIR__ . '/../../Resources/Public/Assets/Icons/';
foreach (glob($iconDir . '*/*.svg') as $file) {
    $relPath = substr($file, strlen($iconDir));
    $iconKey = 'tx-gdmcom-' . str_replace(['/', '.svg'], ['-', ''], $relPath);
    $typeiconClasses[$relPath] = $iconKey;
}

return [
    'ctrl' => [
        'title' => 'Leistung',
        'label' => 'header',
        'iconfile' => 'EXT:gdmcom/Resources/Public/Assets/Icons/services/processing.svg',

        'versioningWS' => false,
        'rootLevel' => 0,

        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'delete' => 'deleted',
        'default_sortby' => 'header',
        'enablecolumns' => [
            'disabled' => 'hidden',
        ],

        'typeicon_column' => 'icon',
        'typeicon_classes' => $typeiconClasses,
    ],

    'columns' => [
        'header' => [
            'label' => 'Überschrift',
            'config' => [
                'type' => 'input',
            ],
        ],

        'bodytext' => [
            'label' => 'Text',
            'config' => [
                'type' => 'text',
                'cols' => 80,
                'rows' => 15,
                'softref' => 'typolink_tag,email[subst],url',
                'enableRichtext' => true,
            ],
        ],

        'icon' => [
            'label' => 'Icon',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'fieldWizard' => [
                    'selectIcons' => [
                        'disabled' => 0,
                    ],
                ],
                'fileFolderConfig' => [
                    'folder' => 'EXT:gdmcom/Resources/Public/Assets/Icons/services',
                ],
                'items' => [
                    [
                        'label' => 'Kein Icon',
                        'value' => '',
                        'icon' => '',
                        'group' => '',
                        'description' => '',
                    ],
                ],
            ],
        ],

        'link' => [
            'label' => 'Link',
            'config' => [
                'type' => 'input',
                'nullable' => 0,
                'type' => 'link',
                'softref' => 'typolink',
                'allowedTypes' => [
                    'page',
                    'url',
                    'file',
                    'record',
                ],
            ],
        ],

        'categories' => [
            'label' => 'Kategorien',
            'config' => [
                'type' => 'category',
                'treeConfig' => [
                    'appearance' => [
                        'nonSelectableLevels' => '0,1',
                    ],
                    'startingPoints' => '###SITE:settings.categories.jobCompany###'
                        . ',###SITE:settings.categories.serviceCategories###'
                ],
            ],
        ],

        'hidden' =>  [
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.enabled',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'default' => 0,
                'items' => [
                    0 => [
                        'label' => '',
                        'invertStateDisplay' => true,
                    ],
                ],
            ],
        ],

    ],

    'types' => [
        '0' => [
            'showitem' => ''
                . '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,'
                . ',header'
                . ',bodytext'
                . ',icon'
                . ',link'
                . ',categories'

                . ',--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access'
                . ',hidden',

        ],
    ],
];
