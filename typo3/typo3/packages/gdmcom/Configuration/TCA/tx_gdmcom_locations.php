<?php

//'services/processing.svg' => 'tx-gdmcom-services-processing'
$typeiconClasses = [
    'default' => 'tx-gdmcom-services-processing',
];

$iconDir = __DIR__ . '/../../Resources/Public/Assets/Icons/';
foreach (glob($iconDir . '*/*.svg') as $file) {
    $relPath = substr($file, strlen($iconDir));
    $iconKey = 'tx-gdmcom-' . str_replace(['/', '.svg'], ['-', ''], $relPath);
    $typeiconClasses[$relPath] = $iconKey;
}

return [
    'ctrl' => [
        'title' => 'Standort',
        'label' => 'title',
        'iconfile' => 'EXT:gdmcom/Resources/Public/Assets/Icons/services/map-pin.svg',

        'versioningWS' => false,
        'rootLevel' => 0,

        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'delete' => 'deleted',
        'default_sortby' => 'title',
        'enablecolumns' => [
            'disabled' => 'hidden',
        ],

        'typeicon_column' => 'icon',
        'typeicon_classes' => $typeiconClasses,
    ],

    'columns' => [
        'title' => [
            'label' => 'Name vom Standort',
            'config' => [
                'type' => 'input',
            ],
        ],

        'headquarter' => [
            'exclude' => 0,
            'label' => 'Hauptsitz',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
            ],
        ],

        'address' => [
            'exclude' => 1,
            'label' => 'Volle Addresse',
            'config' => [
                'type' => 'input',
                'required' => 1,
                'size' => 30,
                'eval' => 'trim'
            ],
        ],

        'loc_lat' => [
            'label' => 'Breitengrad',
            'config' => [
                //"number:decimal" only supports 2 decimal places
                'type' => 'input',
                'nullable' => true,
                'eval' => 'trim'
            ]
        ],
        'loc_lng' => [
            'label' => 'Längengrad',
            'config' => [
                //"number:decimal" only supports 2 decimal places
                'type' => 'input',
                'nullable' => true,
                'eval' => 'trim'
            ]
        ],

        'email' => [
            'label' => 'E-Mail',
            'config' => [
                'type' => 'link',
                'allowedTypes' => [
                    'email'
                ],
            ],
        ],

        'telephone' => [
            'label' => 'Telefon',
            'config' => [
                'type' => 'link',
                'allowedTypes' => [
                    'telephone'
                ],
            ],
        ],

        'website' => [
            'label' => 'Website',
            'config' => [
                'type' => 'link',
                'allowedTypes' => [
                    'page',
                    'url'
                ],
            ],
        ],

        'categories' => [
            'label' => 'Kategorien',
            'config' => [
                'type' => 'category',
                'treeConfig' => [
                    'appearance' => [
                        'nonSelectableLevels' => '0',
                    ],
                    'startingPoints' => '###SITE:settings.categories.jobCompany###'
                ],
            ],
        ],

        'hidden' =>  [
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.enabled',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'default' => 0,
                'items' => [
                    0 => [
                        'label' => '',
                        'invertStateDisplay' => true,
                    ],
                ],
            ],
        ],

    ],

    'palettes' => [
        '1' => ['showitem' => ''],
        'address' => [
            'showitem' => 'address'
                . ',--linebreak--, loc_lat, loc_lng'
        ],
        'contact' => [
            'showitem' => 'email, telephone, website'
        ],
    ],

    'types' => [
        '0' => [
            'showitem' => ''
                . '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,'
                . ', title, headquarter'
                . ', --palette--;Adresse;address'
                . ', --palette--;Kontakt;contact'
                . ', categories'

                . ',--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access'
                . ',hidden',

        ],
    ],
];
