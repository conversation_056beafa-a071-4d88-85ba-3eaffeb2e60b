<?xml version="1.0" encoding="utf-8"?>
<T3DataStructure>
  <sheets>
    <sDEF>
      <ROOT>
        <sheetTitle>ServiceList plugin configuration</sheetTitle>
        <type>array</type>
        <el>
          <settings.companies>
            <label>Anzuzeigende Unternehmen</label>
            <description>Keine Auswahl: Leistungen aller Unternehmen werden angezeigt</description>
            <config>
              <type>category</type>
              <size>12</size>
              <treeConfig>
                <startingPoints>###SITE:settings.categories.jobCompany###</startingPoints>
                <appearance>
                  <nonSelectableLevels>0</nonSelectableLevels>
                </appearance>
              </treeConfig>
            </config>
          </settings.companies>

          <settings.serviceCategory>
            <label>Anzuzeigende Leistungskategorie</label>
            <description>Keine Auswahl: Alle Leistungskategorien werden angezeigt</description>
            <config>
              <type>category</type>
              <size>12</size>
              <relationship>oneToOne</relationship>
              <maxitems>1</maxitems>
              <treeConfig>
                <startingPoints>###SITE:settings.categories.serviceCategories###</startingPoints>
                <appearance>
                  <nonSelectableLevels>0</nonSelectableLevels>
                </appearance>
              </treeConfig>
            </config>
          </settings.serviceCategory>
        </el>
      </ROOT>
    </sDEF>
  </sheets>
</T3DataStructure>
