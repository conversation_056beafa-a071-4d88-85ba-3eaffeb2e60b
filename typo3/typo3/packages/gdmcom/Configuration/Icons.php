<?php

use TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider;

$icons = [];

//register all available service icons
$iconDir = __DIR__ . '/../Resources/Public/Assets/Icons/';
foreach (glob($iconDir . '*/*.svg') as $file) {
    $relPath = substr($file, strlen($iconDir));
    $key = 'tx-gdmcom-' . str_replace(['/', '.svg'], ['-', ''], $relPath);
    $icons[$key] = [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:gdmcom/Resources/Public/Assets/Icons/' . $relPath
    ];
}

return $icons;
