{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"card": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_307bb79e7fa73", "tx_mask_78aeeacd9989a", "bodytext", "tx_mask_56ea322040593", "tx_mask_d413963266a81", "assets", "tx_mask_layout"], "columnsOverride": {"assets": {"allowedFileExtensions": "common-image-types", "config": {"appearance": {"elementBrowserEnabled": 1, "enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "sort": 0}, "fileByUrlAllowed": 0, "fileUploadAllowed": 1, "useSortable": 1}, "maxitems": "1", "minitems": "0"}, "fullKey": "assets", "key": "assets", "type": "media"}, "bodytext": {"config": {"enableRichtext": 1}, "fullKey": "bodytext", "key": "bodytext", "type": "richtext"}, "header_layout": {"config": {"default": "104", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "tx_mask_icon": {"config": {"fieldWizard": {"selectIcons": {"disabled": 0}}, "fileFolderConfig": {"folder": "EXT:gdmcom/Resources/Public/Assets/Icons"}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON>", "value": ""}], "renderType": "selectSingle"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_layout": {"config": {"default": "1", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "Bild über Text", "value": "1"}, {"description": "", "group": "", "icon": "", "label": "Bild neben Text (horizontal)", "value": "2"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_layout", "key": "layout", "type": "select"}, "tx_mask_link": {"config": {"allowedTypes": ["page", "record", "url", "file", "email", "telephone"]}, "fullKey": "tx_mask_link", "key": "link", "type": "link"}, "tx_mask_logo": {"config": {"default": "0", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON>", "value": "0"}, {"description": "", "group": "", "icon": "", "label": "GDMcom", "value": "gdmcom"}, {"description": "", "group": "", "icon": "", "label": "GDMcom | gruppe", "value": "group"}, {"description": "", "group": "", "icon": "", "label": "GDMcom | planung", "value": "planning"}, {"description": "", "group": "", "icon": "", "label": "GDMcom | bau", "value": "construction"}, {"description": "", "group": "", "icon": "", "label": "GIBY", "value": "giby"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_logo", "key": "logo", "type": "select"}}, "description": "", "descriptions": ["", "", "", "", "", "", ""], "icon": "", "iconOverlay": "", "key": "card", "label": "Card", "labels": ["Kopfberei<PERSON>", "Überschriftbereich", "Der Text unterhalb der Überschrift lautet?", "", "Media", "Bilder", "Medien/Image Textanordnung"], "shortLabel": "", "sorting": 1}}, "palettes": {"tx_mask_307bb79e7fa73": {"description": "", "label": "Kopfberei<PERSON>", "showitem": ["tx_mask_logo", "tx_mask_d7fbfd7ca51d8", "tx_mask_icon", "tx_mask_450af2df19f37", "tx_mask_badge"]}, "tx_mask_56ea322040593": {"description": "", "label": "", "showitem": ["tx_mask_link"]}, "tx_mask_78aeeacd9989a": {"description": "", "label": "Überschriftbereich", "showitem": ["header", "tx_mask_7973ecce4f62", "header_layout"]}}, "sql": {"tx_mask_badge": {"tt_content": {"tx_mask_badge": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_icon": {"tt_content": {"tx_mask_icon": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_layout": {"tt_content": {"tx_mask_layout": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_link": {"tt_content": {"tx_mask_link": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_logo": {"tt_content": {"tx_mask_logo": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"assets": {"coreField": 1, "description": {"card": ""}, "fullKey": "assets", "key": "assets", "type": "media"}, "bodytext": {"bodytextTypeByElement": {"accordion": "richtext", "card": "richtext", "content_box": "richtext", "content_snippet": "richtext", "product_card": "richtext", "social_media_list": "text", "text": "richtext"}, "coreField": 1, "fullKey": "bodytext", "key": "bodytext"}, "header": {"coreField": 1, "description": {"card": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"card": "tx_mask_78aeeacd9989a"}, "key": "header", "label": {"card": "Überschrift"}, "order": {"card": 1}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"card": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"card": "tx_mask_78aeeacd9989a"}, "key": "header_layout", "label": {"card": "Verhalten/Aussehen der Überschrift"}, "order": {"card": 3}, "type": "select"}, "tx_mask_307bb79e7fa73": {"config": {"type": "palette"}, "fullKey": "tx_mask_307bb79e7fa73", "key": "307bb79e7fa73", "type": "palette"}, "tx_mask_450af2df19f37": {"config": {"type": "linebreak"}, "description": {"card": ""}, "fullKey": "tx_mask_450af2df19f37", "inPalette": 1, "inlineParent": {"card": "tx_mask_307bb79e7fa73"}, "key": "450af2df19f37", "label": {"card": ""}, "order": {"card": 4}, "type": "linebreak"}, "tx_mask_56ea322040593": {"config": {"type": "palette"}, "fullKey": "tx_mask_56ea322040593", "key": "56ea322040593", "type": "palette"}, "tx_mask_78aeeacd9989a": {"config": {"type": "palette"}, "fullKey": "tx_mask_78aeeacd9989a", "key": "78aeeacd9989a", "type": "palette"}, "tx_mask_7973ecce4f62": {"config": {"type": "linebreak"}, "description": {"card": ""}, "fullKey": "tx_mask_7973ecce4f62", "inPalette": 1, "inlineParent": {"card": "tx_mask_78aeeacd9989a"}, "key": "7973ecce4f62", "label": {"card": ""}, "order": {"card": 2}, "type": "linebreak"}, "tx_mask_badge": {"config": {"nullable": 0, "type": "input"}, "description": {"card": ""}, "fullKey": "tx_mask_badge", "inPalette": 1, "inlineParent": {"card": "tx_mask_307bb79e7fa73"}, "key": "badge", "label": {"card": "Badge"}, "order": {"card": 5}, "type": "string"}, "tx_mask_d413963266a81": {"config": {"type": "tab"}, "fullKey": "tx_mask_d413963266a81", "key": "d413963266a81", "type": "tab"}, "tx_mask_d7fbfd7ca51d8": {"config": {"type": "linebreak"}, "description": {"card": ""}, "fullKey": "tx_mask_d7fbfd7ca51d8", "inPalette": 1, "inlineParent": {"card": "tx_mask_307bb79e7fa73"}, "key": "d7fbfd7ca51d8", "label": {"card": ""}, "order": {"card": 2}, "type": "linebreak"}, "tx_mask_icon": {"config": {"type": "select"}, "description": {"card": ""}, "fullKey": "tx_mask_icon", "inPalette": 1, "inlineParent": {"card": "tx_mask_307bb79e7fa73"}, "key": "icon", "label": {"card": "<PERSON><PERSON><PERSON><PERSON> das Icon aus"}, "order": {"card": 3}, "type": "select"}, "tx_mask_layout": {"config": {"type": "select"}, "fullKey": "tx_mask_layout", "key": "layout", "type": "select"}, "tx_mask_link": {"config": {"nullable": 0, "type": "link"}, "description": {"card": ""}, "fullKey": "tx_mask_link", "inPalette": 1, "inlineParent": {"card": "tx_mask_56ea322040593"}, "key": "link", "label": {"card": "Wohin soll ein <PERSON>ton verlinken?"}, "order": {"card": 1}, "type": "link"}, "tx_mask_logo": {"config": {"type": "select"}, "description": {"card": ""}, "fullKey": "tx_mask_logo", "inPalette": 1, "inlineParent": {"card": "tx_mask_307bb79e7fa73"}, "key": "logo", "label": {"card": "Logo"}, "order": {"card": 1}, "type": "select"}}}}}