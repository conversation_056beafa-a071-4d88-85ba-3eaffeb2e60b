{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"customer_logo": {"color": "#000000", "colorOverlay": "#000000", "columns": ["assets"], "columnsOverride": {"assets": {"allowedFileExtensions": "svg", "config": {"appearance": {"elementBrowserEnabled": 1, "enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "sort": 0}, "fileByUrlAllowed": 0, "fileUploadAllowed": 1, "useSortable": 1}, "minitems": ""}, "fullKey": "assets", "key": "assets", "type": "media"}}, "description": "", "descriptions": [""], "icon": "", "iconOverlay": "", "key": "customer_logo", "label": "Customer <PERSON><PERSON>", "labels": ["Logos"], "shortLabel": "", "sorting": 5}}, "tca": {"assets": {"coreField": 1, "fullKey": "assets", "key": "assets", "type": "media"}}}}}