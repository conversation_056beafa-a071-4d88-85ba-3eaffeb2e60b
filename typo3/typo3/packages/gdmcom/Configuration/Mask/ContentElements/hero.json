{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"hero": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_82b01b60e3282", "tx_mask_f3f5680f8e49e", "assets"], "columnsOverride": {"assets": {"allowedFileExtensions": "jpg,jpeg,png,svg,webp", "config": {"appearance": {"elementBrowserEnabled": 1, "enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "sort": 0}, "fileByUrlAllowed": 1, "fileUploadAllowed": 1, "useSortable": 1}, "minitems": ""}, "fullKey": "assets", "key": "assets", "type": "media"}, "tx_mask_description": {"config": {"enableRichtext": 1}, "fullKey": "tx_mask_description", "key": "description", "type": "richtext"}, "tx_mask_header": {"config": {"max": "40"}, "fullKey": "tx_mask_header", "key": "header", "type": "string"}}, "description": "", "descriptions": ["", "", ""], "icon": "", "iconOverlay": "", "key": "hero", "label": "Hero", "labels": ["", "Media", "Bilder"], "shortLabel": "", "sorting": 1}}, "palettes": {"tx_mask_82b01b60e3282": {"description": "", "label": "", "showitem": ["tx_mask_header", "tx_mask_57dbc6f2488ed", "tx_mask_description"]}}, "sql": {"tx_mask_description": {"tt_content": {"tx_mask_description": "mediumtext"}}, "tx_mask_header": {"tt_content": {"tx_mask_header": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"assets": {"coreField": 1, "fullKey": "assets", "key": "assets", "type": "media"}, "tx_mask_57dbc6f2488ed": {"config": {"type": "linebreak"}, "description": {"hero": ""}, "fullKey": "tx_mask_57dbc6f2488ed", "inPalette": 1, "inlineParent": {"hero": "tx_mask_82b01b60e3282"}, "key": "57dbc6f2488ed", "label": {"hero": ""}, "order": {"hero": 2}, "type": "linebreak"}, "tx_mask_82b01b60e3282": {"config": {"type": "palette"}, "fullKey": "tx_mask_82b01b60e3282", "key": "82b01b60e3282", "type": "palette"}, "tx_mask_description": {"config": {"type": "text"}, "description": {"hero": ""}, "fullKey": "tx_mask_description", "inPalette": 1, "inlineParent": {"hero": "tx_mask_82b01b60e3282"}, "key": "description", "label": {"hero": "Description"}, "order": {"hero": 3}, "type": "richtext"}, "tx_mask_f3f5680f8e49e": {"config": {"type": "tab"}, "fullKey": "tx_mask_f3f5680f8e49e", "key": "f3f5680f8e49e", "type": "tab"}, "tx_mask_header": {"config": {"nullable": 0, "type": "input"}, "description": {"hero": ""}, "fullKey": "tx_mask_header", "inPalette": 1, "inlineParent": {"hero": "tx_mask_82b01b60e3282"}, "key": "header", "label": {"hero": "Header"}, "order": {"hero": 1}, "type": "string"}}}}}