{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"social_media_list": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_23ce91ad42732"], "description": "", "descriptions": [""], "icon": "", "iconOverlay": "", "key": "social_media_list", "label": "Social Media Liste", "labels": ["Wähle das Social Media Icon aus"], "shortLabel": "", "sorting": 2}}, "palettes": {"tx_mask_23ce91ad42732": {"description": "", "label": "Wähle das Social Media Icon aus", "showitem": ["tx_mask_icons"]}}, "sql": {"tx_mask_icons": {"tt_content": {"tx_mask_icons": "int(11) unsigned DEFAULT '0' NOT NULL"}}}, "tca": {"tx_mask_23ce91ad42732": {"config": {"type": "palette"}, "fullKey": "tx_mask_23ce91ad42732", "key": "23ce91ad42732", "type": "palette"}, "tx_mask_icons": {"config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_field": "parentid", "foreign_sortby": "sorting", "foreign_table": "--inlinetable--", "foreign_table_field": "parenttable", "type": "inline"}, "description": {"social_media_list": ""}, "fullKey": "tx_mask_icons", "inPalette": 1, "inlineParent": {"social_media_list": "tx_mask_23ce91ad42732"}, "key": "icons", "label": {"social_media_list": "Icons"}, "order": {"social_media_list": 1}, "type": "inline"}}}, "tx_mask_icons": {"sql": {"tx_mask_icon": {"tx_mask_icons": {"tx_mask_icon": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_link": {"tx_mask_icons": {"tx_mask_link": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"tx_mask_icon": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "fileFolderConfig": {"folder": "EXT:gdmcom/Resources/Public/Assets/Icons/default/social"}, "items": [], "renderType": "selectSingle", "type": "select"}, "fullKey": "tx_mask_icon", "inlineParent": "tx_mask_icons", "key": "icon", "label": "<PERSON><PERSON><PERSON><PERSON> das Icon aus", "order": 1, "type": "select"}, "tx_mask_link": {"config": {"nullable": 0, "type": "link"}, "fullKey": "tx_mask_link", "inlineParent": "tx_mask_icons", "key": "link", "label": "Wohin soll ein Icon verlinken?", "order": 2, "type": "link"}}}}}