{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"carousel": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_layout", "tx_mask_cards"], "columnsOverride": {"tx_mask_cards": {"cTypes": ["mask_card"], "config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_sortby": "sorting", "overrideChildTca": {"columns": {"colPos": {"config": {"default": 999}}}}}, "fullKey": "tx_mask_cards", "key": "cards", "type": "content"}, "tx_mask_layout": {"config": {"default": "1", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "4 Spalten", "value": "4"}, {"description": "", "group": "", "icon": "", "label": "3 Spalten", "value": "3"}, {"description": "", "group": "", "icon": "", "label": "1 Spalte", "value": "1"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_layout", "key": "layout", "type": "select"}}, "description": "", "descriptions": ["Das Carousel zeigt die Slides nebeneinander an", "Bitte füge weitere Inhaltselemente (Card) zum Carousel hinzu\n"], "icon": "", "iconOverlay": "", "key": "carousel", "label": "Carousel", "labels": ["Carousel Layout Optionen", "Cards"], "shortLabel": "", "sorting": 5}}, "sql": {"tx_mask_cards": {"tt_content": {"tx_mask_cards": "int(11) unsigned DEFAULT '0' NOT NULL"}}, "tx_mask_layout": {"tt_content": {"tx_mask_layout": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"tx_mask_cards": {"cTypes": ["mask_card"], "config": {"foreign_table": "tt_content", "type": "inline"}, "fullKey": "tx_mask_cards", "key": "cards", "type": "content"}, "tx_mask_layout": {"config": {"type": "select"}, "fullKey": "tx_mask_layout", "key": "layout", "type": "select"}}}}}