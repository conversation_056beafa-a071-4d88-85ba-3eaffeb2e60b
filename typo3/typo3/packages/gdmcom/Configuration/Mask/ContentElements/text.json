{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"text": {"color": "#000000", "colorOverlay": "#000000", "columns": ["bodytext"], "columnsOverride": {"bodytext": {"config": {"enableRichtext": 1}, "fullKey": "bodytext", "key": "bodytext", "type": "richtext"}}, "description": "", "descriptions": [""], "icon": "", "iconOverlay": "", "key": "text", "label": "Text", "labels": [""], "shortLabel": "", "sorting": 4}}, "tca": {"bodytext": {"bodytextTypeByElement": {"accordion": "richtext", "card": "richtext", "content_box": "richtext", "content_snippet": "richtext", "product_card": "richtext", "social_media_list": "text", "text": "richtext"}, "coreField": 1, "fullKey": "bodytext", "key": "bodytext"}}}}}