TCAdefaults {
    pages {
        backend_layout = pagets__gdmComDefault
    }
}

mod.web_layout.BackendLayouts {
    gdmComDefault {
        title = Standardlayout
        config {
            backend_layout {
                doktype = 1
                colCount = 1
                rowCount = 2
                rows {
                    1 {
                        columns {
                            1 {
                                name = Hero (Stage)
                                colPos = 0
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                name = Inhalt
                                colPos = 1
                            }
                        }
                    }
                }
            }
        }
    }

    gdmComEmpty {
        title = Leer
        config {
            backend_layout {
                doktype = 254
                colCount = 0
                rowCount = 0
                rows {
                }
            }
        }
    }

    gdmComServices {
        title = Leistungsliste
        config {
            backend_layout {
                colCount = 0
                rowCount = 0
                rows {
                }
            }
        }
    }

    gdmComJob {
        title = Joblayout
        config {
            backend_layout {
                doktype = 30
                colCount = 1
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Inhalt
                                colPos = 1
                            }
                        }
                    }
                }
            }
        }
    }
}
