mod.wizards.newContentElement.wizardItems {
    // add the content elements to the tab "plugins"
    plugins {
        elements {
            gdmcom_jobcards {
                title = Jobs: Karten
                description = Highlight-Jobs als Karten
                iconIdentifier = content-user
                tt_content_defValues {
                    CType = list
                    list_type = gdmcom_jobcards
                }
            }

            gdmcom_joblist {
                title = Jobs: Liste
                description = Filterbare Arbeitsstellenliste
                iconIdentifier = content-user
                tt_content_defValues {
                    CType = list
                    list_type = gdmcom_joblist
                }
            }

            gdmcom_servicelist {
                title = Leistungsliste
                iconIdentifier = content-user
                tt_content_defValues {
                    CType = list
                    list_type = gdmcom_servicelist
                }
            }

            gdmcom_locations {
                title = Standort Karte
                description = Map mit allen Standorten
                iconIdentifier = content-user
                tt_content_defValues {
                    CType = list
                    list_type = gdmcom_locations
                }
            }
        }

        show := addToList(
            gdmcom_jobcards
            , gdm_joblist
        )
    }
}
