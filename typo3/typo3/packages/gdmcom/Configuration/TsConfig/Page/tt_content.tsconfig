TCAdefaults.tt_content.frame_class = none

TCEFORM.tt_content {
    #remove content element types from dropdown
    CType.removeItems (
        bullets, header, image, table, text, textpic, textmedia, uploads,
        felogin_login,
        menu_abstract, menu_categorized_content, menu_categorized_pages,
        menu_pages, menu_recently_updated, menu_related_pages,
        menu_section_pages,
        menu_sitemap_pages, menu_sitemap, menu_subpages
        div
    )

    ## Hide content element fields
    # General: Headlines
    # header_layout.disabled = 1
    # only keep "standard" (0) and "hidden" (100)
    header_layout.removeItems = 0,1,2,3,4,5
    header_layout.label =
    header_layout.addItems.101 = Headline Typ 1 (H1)
    header_layout.addItems.102 = Headline Typ 2 (H1)
    header_layout.addItems.103 = Headline Typ 3 (H2)
    header_layout.addItems.104 = Headline Typ 4 (H3)
    header_layout.addItems.105 = Headline Typ 5 (H4)
    header_layout.addItems.106 = Headline Typ 6 (H5)
    header_layout.addItems.107 = Headline Typ 7 (H6)

    header_position.disabled = 1
    date.disabled = 1
    header_link.disabled = 1
    subheader.disabled = 0

    ## Layout
    layout.disabled = 1
    frame_class.disabled = 1
    space_before_class.disabled = 1
    space_after_class.disabled = 1

    #Layout: Links
    linkToTop.disabled = 1

    #tx_mask_button_theme.types.mask_content_snippet.removeItems = primary,secondary

    label.disabled = 0
}

mod.wizards.newContentElement.wizardItems {
    mask {
        before = container
    }
    plugins {
        after = container
    }
}

RTE.config.tt_content.bodytext.preset = gdmcom