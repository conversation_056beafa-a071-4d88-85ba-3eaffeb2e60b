plugin.tx_typo3encore {
    settings {
        builds {
            gdmcom = build/gdmcom
            giby = build/giby
        }

        entrypointJsonPath = build/entrypoints.json
        manifestJsonPath = build/manifest.json

        # Throw an exception if the entrypoints.json file is missing or an entry is missing from the data
        # strictMode = 1

        preload {
            # preload all rendered script and link tags automatically via the http2 Link header
            enable = 1
            crossorigin =
        }
    }
}

module.tx_typo3encore.settings < plugin.tx_typo3encore.settings
