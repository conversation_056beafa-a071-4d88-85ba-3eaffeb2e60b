lib.containerElement < lib.contentElement
lib.containerElement {
  templateRootPaths.10 = EXT:gdmcom/Resources/Private/Templates/Container
  dataProcessing {
    200 = B13\Container\DataProcessing\ContainerProcessor
  }
}

tt_content.tx_gdmcom_container_one_column < lib.containerElement
tt_content.tx_gdmcom_container_one_column.templateName = MainContainer

tt_content.tx_gdmcom_container_two_columns < lib.containerElement
tt_content.tx_gdmcom_container_two_columns.templateName = TwoColumnContainer

tt_content.tx_gdmcom_container_three_columns < lib.containerElement
tt_content.tx_gdmcom_container_three_columns.templateName = ThreeColumnContainer

tt_content.tx_gdmcom_container_four_columns < lib.containerElement
tt_content.tx_gdmcom_container_four_columns.templateName = FourColumnContainer