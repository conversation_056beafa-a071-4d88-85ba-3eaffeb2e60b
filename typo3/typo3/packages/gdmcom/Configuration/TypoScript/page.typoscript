page = PAGE
page {
    shortcutIcon = {$gdmcom.theme.favicon_path}/favicon.ico
    typeNum = 0

    meta {
        viewport = width=device-width, initial-scale=1, shrink-to-fit=no

        X-UA-Compatible = IE=edge
        X-UA-Compatible.attribute = http-equiv

        content-language = de-de
        content-language.attribute = http-equiv

        msapplication-TileColor = {$gdmcom.theme.color}
        theme-color = {$gdmcom.theme.color}
    }

    headerData {
        10 = COA
        10 {
            1 < lib.favicons.link
            1.file = {$gdmcom.theme.favicon_path}/apple-touch-icon.png
            1.stdWrap.wrap = <link rel="apple-touch-icon" sizes="180x180" href="|">

            2 < lib.favicons.link
            2.file = {$gdmcom.theme.favicon_path}/favicon-32x32.png
            2.stdWrap.wrap = <link rel="icon" type="image/png" sizes="32x32" href="|">

            3 < lib.favicons.link
            3.file = {$gdmcom.theme.favicon_path}/favicon-16x16.png
            3.stdWrap.wrap = <link rel="icon" type="image/png" sizes="16x16" href="|">
        }
    }
    
    20 = FLUIDTEMPLATE
    20 {
        templateRootPaths.20 = EXT:gdmcom/Resources/Private/Templates/
        partialRootPaths.20  = EXT:gdmcom/Resources/Private/Partials/
        layoutRootPaths.20   = EXT:gdmcom/Resources/Private/Layouts/

        file.stdWrap.cObject = CASE
        file.stdWrap.cObject {
            key.field = doktype

            default = TEXT
            default.value = EXT:gdmcom/Resources/Private/Templates/Page/Default.html

            30 = TEXT
            30.value = EXT:gdmcom/Resources/Private/Templates/Page/Job.html
        }

        dataProcessing {
            # Main manu
            10 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            10 {
                special = directory
                special.value = {$brand.pids.main_menu}
                levels = 3
                includeSpacer = 0
                as = mainnavigation
            }

            # Content
            20 = TYPO3\CMS\Frontend\DataProcessing\DatabaseQueryProcessor
            20 {
                table = tt_content
                orderBy = sorting
                where = colPos = 0
                as = stageContent
            }

            # Content
            21 = TYPO3\CMS\Frontend\DataProcessing\DatabaseQueryProcessor
            21 {
                table = tt_content
                orderBy = sorting
                where = colPos = 1
                as = mainContent
            }

            # Breadcrumbs
            30 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            30 {
                special = rootline
                special.range = 0|-1
                as = breadcrumbMenu
                includeSpacer = 0
                includeNotInMenu = 0
            }

            # Footer Menu
            40 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            40 {
                special = directory
                special.value = {$brand.pids.footer_menu}
                levels = 3
                includeSpacer = 1
                as = footernavigation
            }
        }
        variables {
            copyright = TEXT
            copyright {
                value = {$gdmcom.theme.copyright}
            }

            socialmediaLinksUid = TEXT
            socialmediaLinksUid {
                value = {$brand.pids.socialmedialinks}
            }

            contactUid = TEXT
            contactUid {
                value = {$brand.pids.contact}
            }

            contactButtonTitle = TEXT
            contactButtonTitle {
                value = {$brand.other.contact_button_title}
            }

            entrypointUid = TEXT
            entrypointUid {
                value = {$brand.pids.entrypoint}
            }

            footerDescription = TEXT
            footerDescription {
                value = {$brand.footer.description}
            }
        }
    }
}
