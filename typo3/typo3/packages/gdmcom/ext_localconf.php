<?php

use TYPO3\CMS\Core\Utility\ExtensionManagementUtility;
use TYPO3\CMS\Extbase\Utility\ExtensionUtility;

defined('TYPO3') or die('Access denied.');

/***************
 * Fluid Components
 */
$GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['fluid_components']['namespaces']['Mogic\\GdmCom\\Components'] =
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::extPath(
        'gdmcom', 'Resources/Private/Templates/Components'
    );

/***************
 * General
 */
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addUserTSConfig('
     @import "EXT:gdmcom/Configuration/TsConfig/User/default.tsconfig"
');

$GLOBALS['TYPO3_CONF_VARS']['GFX']['imagefile_ext'] = 'gif,jpg,jpeg,tif,tiff,bmp,pcx,tga,png,pdf,ai,svg,webp';


/***************
 * RTE preset
 */
$GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['gdmcom']
   = 'EXT:gdmcom/Configuration/RTE/Gdmcom.yaml';

ExtensionUtility::configurePlugin(
    'gdmcom',
    'JobCards',
    [\Mogic\GdmCom\Controller\JobsController::class => 'cards'],
    // non-cacheable actions
    [],
);

ExtensionUtility::configurePlugin(
    'gdmcom',
    'JobList',
    [\Mogic\GdmCom\Controller\JobsController::class => 'list'],
    // non-cacheable actions
    [\Mogic\GdmCom\Controller\JobsController::class => 'list'],
);

ExtensionUtility::configurePlugin(
    'gdmcom',
    'ServiceList',
    [\Mogic\GdmCom\Controller\ServicesController::class => 'list'],
    // non-cacheable actions
    [],
);

ExtensionUtility::configurePlugin(
    'gdmcom',
    'Locations',
    [\Mogic\GdmCom\Controller\LocationsController::class => 'map'],
    // non-cacheable actions
    [],
);
