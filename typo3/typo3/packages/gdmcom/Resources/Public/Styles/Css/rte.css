.list-check {

}


.list-check li {
    @apply inline-flex gap-2.5
}



ul li,
ol li {
    margin-bottom: 12px;
}

ol li::marker,
ul li::marker {
    color: var(--gray-900, #1E1E1E);
    /* text-base/font-bold */
    font-family: Asap;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%; /* 24px */
    letter-spacing: 0.32px;
}

ul.checked-list {
    list-style: none;
    display: flex;
    padding: 0px;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
}

ul.checked-list li {
    position: relative;
    padding-left: 2em;
    color: var(--gray-800, #3B3B3B);

    font-family: Asap;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 24px */
}

ul.checked-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 1.5em;
    height: 1.5em;
    background-image: url('../../Assets/Icons/check.svg');
    background-size: contain;
    background-repeat: no-repeat;
}

.linkToEmail,
.linkToExternalPage,
.linkToFile,
.linkToPhone {
    position: relative;
    padding-left: 24px;
    color: black;
    font-family: Asap;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%; /* 21px */
    display: inline-flex;
    align-items: center;
}

.linkToEmail:hover,
.linkToExternalPage:hover,
.linkToFile:hover,
.linkToPhone:hover {
    color: var(--gray-700, #1E1E1E);
}

.linkToEmail::before,
.linkToExternalPage::before,
.linkToFile::before,
.linkToPhone::before {
    content: '';
    position: absolute;
    left: 0;
    width: 16px;
    height: 16px;
    background-size: contain;
    background-repeat: no-repeat;
}

.linkToExternalPage::before {
    background-image: url('../../Assets/Icons/globe.svg');
}

.linkToFile::before {
    background-image: url('../../Assets/Icons/arrow-down-to-bracket.svg');
}

.linkToPhone::before {
    background-image: url('../../Assets/Icons/phone.svg');
}

.linkToEmail::before {
    background-image: url('../../Assets/Icons/mail.svg');
}

.linkToExternalPage:hover::before,
.linkToEmail:hover::before,
.linkToFile:hover::before,
.linkToPhone:hover::before{
    filter: invert(33%) sepia(0%) saturate(1809%) hue-rotate(146deg) brightness(99%) contrast(90%);
}
