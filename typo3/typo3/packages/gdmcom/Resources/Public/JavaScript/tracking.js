function load_matomo_script(e) {
    // save user's choice in a local variable
    var consent = e.detail || {};

    // check if our service has been accepted
    if(consent["matomo"]) {
        var _paq = window._paq = window._paq || [];
        /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
        _paq.push(['trackPageView']);
        _paq.push(['enableLinkTracking']);
        (function() {
        var u="//statistics.gdmcom-gruppe.de/";
        _paq.push(['setTrackerUrl', u+'matomo.php']);
        _paq.push(['setSiteId', matomoSiteId]);
        var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
        g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
        })();
    }
}

// execute the callback function when banner sends consent events
window.addEventListener("cf_services_consent", load_matomo_script);
window.addEventListener("cf_services_consent_loaded", load_matomo_script);
