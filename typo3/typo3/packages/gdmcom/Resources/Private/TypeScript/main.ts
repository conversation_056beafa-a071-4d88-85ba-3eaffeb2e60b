import "../Css/main.css";

import Alpine from 'alpinejs'
import checkbox from "./modules/checkbox";
import navigation from "./modules/navigation";
import radio from "./modules/radio";
import list from "./modules/list";
import carousel from "./modules/carousel";
import maps from "./modules/maps";
import { showCookiefirstBannerSettingsBasedOnHash } from "./utils/cookiefirst";

Alpine.data("checkbox", checkbox)
Alpine.data("navigation", navigation)
Alpine.data("radio", radio)
Alpine.data("list", list)
Alpine.data("radio", radio)
Alpine.data('carousel', carousel)
Alpine.data('maps', maps)

Alpine.start()

const cookie_banner_show_settings_hash = "#show_cookiebanner_settings"
showCookiefirstBannerSettingsBasedOnHash(cookie_banner_show_settings_hash)
window.addEventListener("hashchange", () => showCookiefirstBannerSettingsBasedOnHash(cookie_banner_show_settings_hash))
