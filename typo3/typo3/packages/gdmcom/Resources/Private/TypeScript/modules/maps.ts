import { GeoJSON, geo<PERSON>son, LatLngBoundsExpression, LatLngExpression, LatLngTuple, Map, marker, Util, Marker, Layer } from 'leaflet'
import { toggleList } from '../lib/utilities'
import { defineComponent } from '../utils/alpinejs'
import { FeatureCollection } from 'geojson'
import { ILocationItemData, IMapData, TLocationFeature, TBuildMarkerOptions } from '../types/maps';
import { createMarkerIcon, getCenterFromCountry, removeLayersFromMap, resetMap } from '../lib/leaflet'

const ym = window.ym ?? null

export default defineComponent(
  (data: IMapData) => ({
    map: {} as Map,
    activeLocationUid: 0,
    activeFilterModel: data.activeFilterModel ?? [],
    mapInitialized: false,
    geoJsonLayer: {} as GeoJSON,
    options: {
      zoom: 6,
      center: getCenterFromCountry("germany"),
      ...data.options
    },
    currentBounds: [] as LatLngBoundsExpression,

    locationFilterBinding: {
      ['x-on:change'] (event: any) {
        const selectedId = Number(event.target.value)
        this.activeFilterModel = toggleList(this.activeFilterModel, selectedId)
      },
    },

    locationItemBinding: {
      ':class'() {
        const self = this
        const target = self as (typeof self) & ILocationItemData
        const {
          activeLocationUid,
          uid
        } = target

        return {
          'hidden': activeLocationUid != uid
        }
      },
    },


    init() {
      if (!ym) {
        return
      }

      this.mapInitialized = true
      ym.ready(
        () => {
          this.map = ym.map(this.mapElement, {
            center: this.options.center,
            zoom: this.options.zoom
          })
          this.updateMarker(data.poiData)
        }
      )

      this.$watch(
        "activeFilterModel",
        (activeFilterModel) => {
          removeLayersFromMap(
            this.geoJsonLayer.getLayers(),
            this.map
          )


          const poiData = {...data.poiData}
          const initialFilterModel = data.activeFilterModel ?? []

          if (initialFilterModel.length !== activeFilterModel.length) {
            poiData.features = poiData.features.filter(
              (feature: TLocationFeature) => activeFilterModel.includes(feature.properties.companyId)
            )
          }

          this.updateMarker(poiData)
        }
      )
    },

    updateMarker(data: FeatureCollection) {
      if (data.features.length === 0) {
        resetMap(
          this.map,
          {
            zoom: this.options.zoom,
            center: this.options.center
          }
        )
        return
      }

      const _self = this
      this.geoJsonLayer = geoJson(
        data,
        {
          pointToLayer: function(geoJsonPoint: TLocationFeature, latlng: LatLngExpression) {
            const m = marker(latlng)

            const initialIcon = _self.resetOrInitMarkerIcon.call(
              _self,
              !!geoJsonPoint.properties.headquarter
            )
            m.setIcon(initialIcon)

            m.on('click', _self.showLocationCard.bind(
              _self,
              geoJsonPoint,
              latlng as LatLngTuple,
              m
            ))

            return m
          }
        }
      ).addTo(_self.map)

      _self.fitBoundsToGeoJsonLayer()
    },
    fitBoundsToGeoJsonLayer() {
      this.map.fitBounds(this.geoJsonLayer.getBounds(), {
        padding: [24,24]
      })
    },
    showLocationCard(geoJsonPoint: TLocationFeature, latlng: LatLngTuple, m: Marker) {
      this.activeLocationUid = geoJsonPoint.properties.uid
      this.map.fitBounds([latlng], {
        paddingBottomRight: [320,320],
        paddingTopLeft: [12,12],
        maxZoom: 17
      })  

      this.resetMarkerIcons()
      const activeIcon = this.activateMarkerIcon(geoJsonPoint.properties.headquarter)
      m.setIcon(activeIcon)
    },  
    hideLocationCard(resetToGeoJsonBounds: boolean = true) {
      this.activeLocationUid = 0
      
      if (!!resetToGeoJsonBounds) {
        this.fitBoundsToGeoJsonLayer()
      }

      this.resetMarkerIcons()
    },

    resetMarkerIcons() {
      this.markers.forEach(
        marker => marker.setIcon(
          this.resetOrInitMarkerIcon(
            marker.feature?.properties.headquarter
          )
        )
      )
    },

    buildMarkerIcon(markerOptions: TBuildMarkerOptions) {
      const iconSize:number = !!markerOptions.isLarge
        ? 56
        : 40

      const iconHtml = markerOptions.isActive
        ? this.markerIconActiveElement.innerHTML
        : this.markerIconElement.innerHTML

      return createMarkerIcon({
        html: Util.template(iconHtml, {}),
        iconSize: [iconSize, iconSize],
        iconAnchor: [iconSize / 2, iconSize],
        className: "bg-transparent"
      })
    },

    activateMarkerIcon(isLarge: boolean = false) {
      return this.buildMarkerIcon({
        isActive: true,
        isLarge
      })
    },

    resetOrInitMarkerIcon(isLarge: boolean = false) {
      return this.buildMarkerIcon({
        isActive: false,
        isLarge
      })
    },

    get mapElement(): HTMLElement {
      return this.$refs.map as HTMLElement
    },
    get markerIconElement(): HTMLElement {
      return this.$refs.defaultMarker as HTMLElement
    },
    get markerIconActiveElement(): HTMLElement {
      return this.$refs.activeMarker as HTMLElement
    },
    get markers(): Marker[] {
      const markers: Marker[] = []
      this.map.eachLayer(
        (l: Layer) => {
          if (l instanceof Marker) {
            markers.push(l as Marker)
          }
        }
      )
      return markers;
    }
  })
)
