import { defineComponent } from '../utils/alpinejs'

export default defineComponent(
  (data: IRadioData) => ({
    checked: data.checked,
    disabled: data.disabled,

    input: {
      ['x-on:change'](event: any) {
        // This is an example for binding attributes on input element
      },
    },

    get inputElement(): HTMLInputElement {
      return  this.$refs.input as HTMLInputElement
    }
  })
)
