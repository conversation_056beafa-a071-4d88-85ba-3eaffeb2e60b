import { defineComponent } from '../utils/alpinejs'

export default defineComponent(
  (data: INavigationData = {}) => ({
    showMenuMobile: data.showMenuMobile,
    openSubmenus: new Set<string>(),
    hoverTimeouts: <Record<string, ReturnType<typeof setTimeout>>>{},

    openSubmenu(submenu: string) {
      if (this.hoverTimeouts[submenu]) {
        clearTimeout(this.hoverTimeouts[submenu])
        delete this.hoverTimeouts[submenu]
      }

      if (!this.isSubmenuOpen(submenu)) {
        this.openSubmenus.add(submenu)
      }
    },

    closeSubmenu(submenu: string) {
      this.openSubmenus.delete(submenu)
      if (this.hoverTimeouts[submenu]) {
        clearTimeout(this.hoverTimeouts[submenu])
        delete this.hoverTimeouts[submenu]
      }
    },

    closeSubmenuWithDelay(submenu: string, delay: number = 50) {
      if (this.hoverTimeouts[submenu]) {
        clearTimeout(this.hoverTimeouts[submenu])
      }

      this.hoverTimeouts[submenu] = setTimeout(() => {
        this.closeSubmenu(submenu)
      }, delay)
    },

    isSubmenuOpen(submenu: string) {
      return this.openSubmenus.has(submenu)
    },

    handleMouseEnter(submenu: string) {
      if (!this.showMenuMobile) {
        this.openSubmenu(submenu)
      }
    },

    handleMouseLeave(submenu: string) {
      if (!this.showMenuMobile) {
        this.closeSubmenuWithDelay(submenu, 50)
      }
    },

    handleToggleSubmenu(submenu: string) {
      if (this.isSubmenuOpen(submenu)) {
        this.closeSubmenu(submenu)
      } else {
        this.openSubmenu(submenu)
      }
    }
  })
)
