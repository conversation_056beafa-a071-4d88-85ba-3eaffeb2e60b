import { defineComponent } from '../utils/alpinejs'

// Hybrid approach: Simple JS logic + CSS-driven animations
export default defineComponent(
  (data: INavigationData = {}) => ({
    showMenuMobile: data.showMenuMobile,
    openSubmenus: new Set<string>(),
    hoverTimeouts: <Record<string, ReturnType<typeof setTimeout>>>{},

    // Core logic - let CSS handle animations
    openSubmenu(submenu: string) {
      if (this.hoverTimeouts[submenu]) {
        clearTimeout(this.hoverTimeouts[submenu])
        delete this.hoverTimeouts[submenu]
      }

      if (!this.isSubmenuOpen(submenu)) {
        this.openSubmenus.add(submenu)
        // Trigger CSS animation by setting data attribute
        this.updateSubmenuDOM(submenu, true)
      }
    },

    closeSubmenu(submenu: string) {
      this.openSubmenus.delete(submenu)
      // Trigger CSS animation by removing data attribute
      this.updateSubmenuDOM(submenu, false)

      if (this.hoverTimeouts[submenu]) {
        clearTimeout(this.hoverTimeouts[submenu])
        delete this.hoverTimeouts[submenu]
      }
    },

    closeSubmenuWithDelay(submenu: string, delay: number = 150) {
      if (this.hoverTimeouts[submenu]) {
        clearTimeout(this.hoverTimeouts[submenu])
      }

      this.hoverTimeouts[submenu] = setTimeout(() => {
        this.closeSubmenu(submenu)
      }, delay)
    },

    // Helper to update DOM for CSS animations
    updateSubmenuDOM(submenu: string, isOpen: boolean) {
      // Update submenu data attribute for CSS
      const submenuElement = document.querySelector(`[data-submenu="${submenu}"]`)
      if (submenuElement) {
        submenuElement.setAttribute('data-open', isOpen.toString())
      }

      // Update chevron data attribute for CSS rotation
      const chevronElement = document.querySelector(`[data-chevron="${submenu}"]`)
      if (chevronElement) {
        chevronElement.setAttribute('data-open', isOpen.toString())
      }
    },

    isSubmenuOpen(submenu: string) {
      return this.openSubmenus.has(submenu)
    },

    // Event handlers remain simple
    handleMouseEnter(submenu: string) {
      if (!this.showMenuMobile) {
        this.openSubmenu(submenu)
      }
    },

    handleMouseLeave(submenu: string) {
      if (!this.showMenuMobile) {
        this.closeSubmenuWithDelay(submenu, 150)
      }
    },

    handleToggleSubmenu(submenu: string) {
      if (this.isSubmenuOpen(submenu)) {
        this.closeSubmenu(submenu)
      } else {
        this.openSubmenu(submenu)
      }
    },

    // Optional: Close all submenus (useful for mobile)
    closeAllSubmenus() {
      this.openSubmenus.forEach(submenu => {
        this.closeSubmenu(submenu)
      })
    },

    // Optional: Keyboard support
    handleKeyDown(event: KeyboardEvent, submenu: string) {
      if (event.key === 'Escape') {
        this.closeAllSubmenus()
      } else if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault()
        this.handleToggleSubmenu(submenu)
      }
    }
  })
)
