import { toggleList, intersect } from '../lib/utilities';
import { defineComponent } from '../utils/alpinejs'

export default defineComponent(
  (data: IListData) => ({
    limit: data.limit,
    length: data.length,
    visibleCount: data.limit,
    showButton: true,
    activeFilterModel: data.activeFilterModel ?? [],

    init() {
      this.$watch('visibleCount', (value) => {
        this.showButton = this.length > value
      });
    },
    jobFilterBinding: {
      ['x-on:change']() {
        (this.$el as HTMLFormElement).submit();
      },
      ['x-on:reset-filter'](event: any) {
        if (event.detail) {
          const checkboxList = this.$el.querySelectorAll(
            `input[name="${event.detail}"]`
          )

          checkboxList.forEach(checkbox => {
            (checkbox as HTMLInputElement).checked = false
          });
          (this.$el as HTMLFormElement).submit();
        }
      }
    },

    serviceFilterBinding: {
      ['x-on:change'](event: any) {
        const initialFilterModel = data.activeFilterModel ?? []
        const selectedId = Number(event.target.value)
        this.activeFilterModel = toggleList(this.activeFilterModel, selectedId)
        this.showButton = (initialFilterModel.length === this.activeFilterModel.length)
      },
    },

    jobItemBinding: {
      ':class'() {
        const self = this
        const target = self as (typeof self) & IListItemData
        return {
          'hidden': target.itemCycle > target.visibleCount
        }
      }
    },
    serviceItemBinding: {
      ':class'() {
        const self = this
        const target = self as (typeof self) & IListItemData
        const {
          itemCycle,
          activeFilterModel,
          visibleCount,
          categories
        } = target

        const initialFilterModel = data.activeFilterModel ?? []

        if (initialFilterModel.length !== activeFilterModel.length) {
          return {
            'hidden md:hidden': intersect(activeFilterModel, categories).length == 0
          }
        }

        return {
          'hidden': itemCycle > visibleCount
        }
      },
    },

    showMore() {
      this.visibleCount += this.limit;
    },
  })
)
