import { defineComponent } from "../utils/alpinejs";

export default defineComponent((data: ICarouselComponentData) => ({
  options: {
    infinite: false,
    interval: 3000,
    ...data,
  },

  activeIndex: 0,
  slides: 0,
  needsNavigation: false,

  init() {
    this.slides = Math.ceil(
      this.$refs["canvas"].scrollWidth / this.$refs["canvas"].offsetWidth
    );
    this.needsNavigation = this.slides > 1;
    window.addEventListener("resize", () => {
      this.slides = Math.ceil(
        this.$refs["canvas"].scrollWidth / this.$refs["canvas"].offsetWidth
      );
    });

    this.$watch("activeIndex", (value) => {
      this.$refs["canvas"].scrollTo({
        left: this.$refs["canvas"].offsetWidth * value,
        behavior: "smooth",
      });
    });

    this.$watch("slides", (value) => value > 1);
  },

  slideTo(slide: number) {
    this.activeIndex = slide - 1;
  },
  next() {
    this.activeIndex += 1;
  },
  prev() {
    this.activeIndex -= 1;
  },
}));
