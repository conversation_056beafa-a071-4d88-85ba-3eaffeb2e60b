import { Feature, FeatureCollection, Polygon, Point } from "geojson";

export interface IGeoJsonLocationFeature {
    companyId: number,
    headquarter: boolean,
    uid: number
}

export interface IGeoJsonLocationAreaFeature {
    status: string,
    area: string,
    name: string,
    established: string,
    uid: string
}

export type TLocationFeatureCollection = FeatureCollection<Point, IGeoJsonLocationFeature>;
export type TLocationFeature = Feature<Point, IGeoJsonLocationFeature>


export type TLocationAreaFeatureCollection = FeatureCollection<Point, IGeoJsonLocationAreaFeature>;
export type TLocationAreaFeature = Feature<Polygon, IGeoJsonLocationAreaFeature>

export interface IMapData {
    poiData: TFeatureCollection;
    type: 'location';
    filterModel: number[] = [];
    activeFilterModel?: number[] = [];
    options: object
}

export interface IMapAreaData {
    areaData: TFeatureCollection;
    filterModel: number[] = [];
    activeFilterModel?: number[] = [];
    options: object
}

interface ILocationItemData {
    uid: number
}

interface ILocationAreaItemData {
    uid: string
}

type TBuildMarkerOptionsKeys = "isActive" | "isLarge"
export type TBuildMarkerOptions = Record<TBuildMarkerOptions, boolean>
