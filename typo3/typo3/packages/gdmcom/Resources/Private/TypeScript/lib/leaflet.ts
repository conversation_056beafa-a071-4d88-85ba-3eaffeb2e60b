import { divIcon, DivIcon, DivIconOptions, LatLng, Layer, Map } from "leaflet"

export function removeLayersFromMap(layers: Layer[], map: Map) {
  layers.forEach(
    (layer: Layer) => removeLayerFromMap(
      layer,
      map
    )
  )
}

export function removeLayerFromMap(layer: Layer, map: Map) {
  map.removeLayer(layer)
}

export function createMarkerIcon(options: DivIconOptions): DivIcon {
  return divIcon({
    ...options
  });
}

export function getCenterFromCountry(country: "germany"): LatLng {
  return new LatLng(
    51.163361,
    10.447683
  )
}

export type TResetMapOptions = {
  zoom: number,
  center: LatLng
}
export function resetMap(map: Map, options: TResetMapOptions) {
  map.setZoom(options.zoom)
  map.panTo(options.center)
}
