
export function toggleList<T>(
  list: T[],
  item: T,
  findCallback: (item: T) => T = item => item
): T[] {
  const index = list.findIndex(
    i => findCallback(i) === findCallback(item)
  );

  if (index === -1) {
    return [...list, item];
  }

  const copy = [...list];
  copy.splice(index, 1);
  return copy;
};

export function intersect<T>(
  list1: T[],
  list2: T[]
): T[] {
  return list1.filter(
    (value: T) => list2.includes(value)
  );
}
