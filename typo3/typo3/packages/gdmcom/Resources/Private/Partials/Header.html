<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
>
  <f:section name="Main">
    <nav
      x-data="navigation()"
      x-on:click.outside="showMenuMobile = false"
      class="fixed top-0 w-full bg-white z-[1002] shadow-md dark:bg-gray-900"
    >
      <div
        class="max-w-[1440px] flex flex-wrap items-center justify-between py-4 2xl:py-6 w-full relative mx-auto px-content"
      >
        <f:link.typolink
          parameter="{entrypointUid}"
          class="flex flex-1 2xl:flex-[unset] items-center space-x-3 rtl:space-x-reverse"
          additionalAttributes="{aria-label: 'Go to main page'}"
        >
          <f:render partial="Logo" arguments="{_all}" section="Default" />
        </f:link.typolink>

        <div class="mr-4 2xl:mr-0 order-2 2xl:order-3">
          <f:if condition="{contactUid}">
            <f:render
              arguments="{
              pid: contactUid,
              title: contactButtonTitle
            }"
              section="Contact"
            />
          </f:if>
        </div>

        <button
          x-on:click="showMenuMobile = !showMenuMobile"
          type="button"
          class="inline-flex order-3 2xl:hidden dark:text-white"
          aria-controls="navbar-cta"
          aria-expanded="false"
        >
          <span class="sr-only">Öffne Menü</span>
          <span x-show="!showMenuMobile">
            <gdmcomc:icon name="default/bars" class="w-5 h-5 transition-all" />
          </span>
          <span x-show="showMenuMobile" x-cloak="">
            <gdmcomc:icon name="default/close" class="w-5 h-5" />
          </span>
        </button>

        <div
          class="z-50 order-4 w-full 2xl:flex 2xl:w-auto 2xl:order-2"
          x-bind:class="{'hidden': !showMenuMobile, 'block': showMenuMobile}"
          x-cloak=""
        >
          <f:render
            section="Main"
            partial="Navigation/Main"
            arguments="{_all}"
          />
        </div>
      </div>
    </nav>
  </f:section>

  <f:section name="Contact">
    <f:variable
      name="target"
      value="{v:page.info(pageUid: pid, field: 'target')}"
    />

    <gdmcomc:button
      link="{uri: pid, target: target}"
      theme="primary"
      no-icon="1"
      class="px-3 xl:px-5"
    >
      {title}
    </gdmcomc:button>
  </f:section>
</html>
