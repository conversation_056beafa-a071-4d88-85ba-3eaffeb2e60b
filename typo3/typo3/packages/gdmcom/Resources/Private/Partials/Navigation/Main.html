<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-transition="http://example.org/dummy-ns"
  xmlns:x-cloak="http://example.org/dummy-ns"
>
  <f:section name="DropdownOpenTrigger">
    <f:variable name="triggerClassAddition" value="-rotate-90 {level}" />
    <f:variable name="classBinding">
      {
        'rotate-0 2xl:rotate-90': !isSubmenuOpen(submenu),
        '-rotate-90': isSubmenuOpen(submenu)
      }
    </f:variable>
    <f:if condition="{level} > 1">
      <f:variable name="classBinding">
        {
          'rotate-0 2xl:rotate-0': !isSubmenuOpen(submenu),
          '-rotate-90 2xl:rotate-0': isSubmenuOpen(submenu)
        }
      </f:variable>
    </f:if>
    <button
      x-on:click.prevent="handleToggleSubmenu(submenu)"
      class="absolute flex justify-end transition-transform duration-100 ease-in-out -translate-y-1/2 rounded right-4 top-1/2 grow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-secondary-500 size-5"
      x-bind:class="{classBinding}"
    >
      <gdmcomc:icon name="default/chevron-right.svg" class="size-full" />
    </button>
  </f:section>

  <f:section name="MenuItemLink">
    <f:variable name="itemLinkClassAddition" value="px-2 2xl:px-6" />
    <f:if condition="{level} > 1">
      <f:variable name="itemLinkClassAddition" value="px-2" />
    </f:if>
    <f:if condition="{level} > 2">
      <f:variable name="itemLinkClassAddition" value="px-6 2xl:px-2" />
    </f:if>
    <a
      href="{item.link}"
      title="{item.title}"
      target="{item.target}"
      class="text-base hover:font-bold focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-secondary-500 rounded flex items-center justify-between gap-5 relative {itemLinkClassAddition}"
      aria-current="page"
      x-bind:class="{'font-bold dark:bg-gray-700 dark:2xl:bg-gray-900': isSubmenuOpen(submenu)}"
      x-on:mouseenter="handleMouseEnter(submenu)"
      x-on:mouseleave="handleMouseLeave(submenu)"
    >
      <span class="px-4 py-3 2xl:py-2.5">{item.title}</span>
      <f:if condition="{item.children -> f:count()} > 0">
        <f:render section="DropdownOpenTrigger" arguments="{level:level}" />
      </f:if>
    </a>
  </f:section>

  <f:section name="MenuItem">
    <f:variable name="menu" value="menu-level-{level}" />
    <f:variable
      name="itemClassName"
      value="relative w-full 2xl:w-auto z-50 text-gray-900 dark:text-white"
    />
    <f:if condition="{data.uid} == {item.data.uid}">
      <f:variable name="itemClassName" value="{itemClassName} 2xl:underline" />
    </f:if>
    <f:if condition="2 > {level}">
      <f:variable
        name="itemClassName"
        value="{itemClassName} border-t border-gray-200 2xl:border-none"
      />

      <f:if condition="{level} > 1">
        <f:variable name="itemClassName" value="{itemClassName} border-b" />
      </f:if>
    </f:if>

    <li
      class="{itemClassName}"
      x-data="{ submenu: $id('{menu}') }"
      x-on:mouseenter="handleMouseEnter(submenu)"
      x-on:mouseleave="handleMouseLeave(submenu)"
    >
      <f:render
        section="MenuItemLink"
        arguments="{
          item: item,
          menu: menu,
          level: level
        }"
      />

      <f:if condition="{item.children -> f:count()} > 0">
        <f:render
          section="MenuSubListItems"
          arguments="{
          items: item.children,
          menu: menu,
          level: level,
          isLastMenuItem: isLastMenuItem
        }"
        />
      </f:if>
    </li>
  </f:section>

  <f:section name="MenuListItems">
    <ul
      class="absolute w-full left-0 top-full 2xl:relative 2xl:left-[unset] flex flex-col 2xl:flex-row 2xl:gap-8 2xl:justify-between items-center bg-white z-20 dark:bg-gray-900"
    >
      <f:for each="{items}" as="item" iteration="itemIteration">
        <f:render
          section="MenuItem"
          arguments="{
            item: item,
            level: 1,
            isLastMenuItem: itemIteration.isLast
          }"
        />
      </f:for>
    </ul>
  </f:section>

  <f:section name="MenuSubListItems">
    <f:variable name="level" value="{level+1}" />
    <f:variable
      name="subListClassAddition"
      value="2xl:mt-2 bg-white shadow-inner-top-md 2xl:-left-16 w-full 2xl:w-64"
    />
    <f:variable name="lastSubmenuClass" value="" />
    <f:if condition="{level} > 2">
      <f:variable
        name="subListClassAddition"
        value="bg-gray-50 2xl:bg-white 2xl:w-full 2xl:top-0"
      />
      <f:variable name="lastSubmenuClass" value="2xl:left-full 2xl:ml-2" />
      <f:if condition="{isLastMenuItem}">
        <f:variable name="lastSubmenuClass" value="2xl:right-full 2xl:mr-2" />
      </f:if>
    </f:if>

    <ul
      x-on:click.outside="closeSubmenu(submenu)"
      x-on:mouseenter="handleMouseEnter(submenu)"
      x-on:mouseleave="handleMouseLeave(submenu)"
      x-transition:enter="transition ease-in duration-100"
      x-transition:enter-start="opacity-0 2x:transform -translate-y-2 max-h-0 overflow-hidden"
      x-transition:enter-end="opacity-100 transform translate-y-0"
      x-transition:leave="transition ease-out duration-0 duration-75"
      x-transition:leave-start="opacity-100 transform translate-y-0  "
      x-transition:leave-end="opacity-0 transform -translate-y-2 max-h-0"
      class="2xl:z-10 2xl:absolute 2xl:rounded-lg 2xl:shadow py-2 2xl:border 2xl:border-gray-100 {subListClassAddition} {lastSubmenuClass} dark:bg-gray-900 dark:2xl:bg-gray-700 dark:2xl:border-gray-600"
      x-show="isSubmenuOpen(submenu)"
      x-id="['{menu}']"
    >
      <f:for each="{items}" as="item" iteration="i">
        <f:render
          section="MenuItem"
          arguments="{
            item: item,
            level: level,
            isLastMenuItem: isLastMenuItem
          }"
        />
      </f:for>
    </ul>
  </f:section>

  <f:section name="Main">
    <f:render
      section="MenuListItems"
      arguments="{
      items: mainnavigation
    }"
    />
  </f:section>
</html>
