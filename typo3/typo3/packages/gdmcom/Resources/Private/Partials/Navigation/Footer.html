<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/vhs/ViewHelpers"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
>
  <f:section name="Content">
    <div class="flex flex-col col-span-2 gap-5 lg:mr-12">
      <div class="flex flex-col gap-5">
        <f:link.typolink
          parameter="{entrypointUid}"
          class="flex items-start text-gray-100 rounded focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-secondary-300"
          additionalAttributes="{aria-label: 'Go to main page'}"
        >
          <f:render partial="Logo" arguments="{_all}" section="Footer" />
        </f:link.typolink>
        <p class="text-gray-100">{footerDescription}</p>
        <v:content.render contentUids="{0: socialmediaLinksUid}" />
      </div>
    </div>
  </f:section>

  <f:section name="MenuItem">
    <li
      class="text-gray-100 rounded hover:text-secondary-300 hover:underline focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-secondary-300"
    >
      <f:render
        section="MenuItemLink"
        arguments="{
        item: item
      }"
      />
    </li>
  </f:section>

  <f:section name="MenuItemLink">
    <f:variable name="href" value="{item.data.url}" />
    <f:if condition="{item.link}">
      <f:variable name="href" value="{item.link}" />
    </f:if>

    <a href="{href}" title="{item.title}" target="{item.target}">
      {item.title}
    </a>
  </f:section>

  <f:section name="Menu">
    <f:for each="{footernavigation}" as="spacer">
      <div class="col-span-2 lg:col-span-1">
        <f:if condition="{spacer.title}">
          <h2 class="mb-4 text-base font-bold text-white">{spacer.title}</h2>
        </f:if>
        <f:if condition="{spacer.children}">
          <ul class="flex flex-col gap-4">
            <f:for each="{spacer.children}" as="item">
              <f:render
                section="MenuItem"
                arguments="{
                    item: item
                  }"
              />
            </f:for>
          </ul>
        </f:if>
      </div>
    </f:for>
  </f:section>

  <f:section name="Main">
    <div class="grid grid-cols-2 gap-8 mb-12 xl:grid-cols-6">
      <f:render section="Content" arguments="{_all}" />
      <f:render section="Menu" arguments="{_all}" />
    </div>

    <hr class="border-gray-200 sm:mx-auto lg:my-8" />

    <f:link.typolink
      parameter="{entrypointUid}"
      class="block text-sm text-center text-gray-400 hover:text-secondary-300"
    >
      © <f:format.date format="Y">now</f:format.date>
      {copyright}
    </f:link.typolink>
  </f:section>
</html>
