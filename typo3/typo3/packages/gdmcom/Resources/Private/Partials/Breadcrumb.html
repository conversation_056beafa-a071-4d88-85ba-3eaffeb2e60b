<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/Fluid/Vhs"
  xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <f:section name="Main">
    <nav aria-label="Breadcrumb" class="mt-6">
      <ol
        class="flex gap-2 items-center flex-wrap text-gray-600 text-sm lg:text-base dark:text-gray-300"
      >
        <f:for each="{breadcrumbMenu}" as="item" iteration="iterator">
          <f:variable name="itemClass" value="inline-flex gap-2 items-center" />
          <f:variable
            name="labelClass"
            value="flex-shrink-0 truncate whitespace-nowrap hover:text-gray-900 hover:underline hover:underline-offset-4 dark:hover:text-gray-50"
          />
          <f:variable name="labelTag" value="a" />
          <f:variable
            name="labelTagAttributes"
            value="{
          title: item.title,
          href:  item.link
        }"
          />
          <f:variable name="spacer" value="1" />
          <f:if condition="{iterator.isLast}">
            <f:variable
              name="itemClass"
              value="{itemClass} text-black dark:text-white"
            />
            <f:variable name="labelClass" value="" />
            <f:variable name="labelTag" value="strong" />
            <f:variable name="labelTagAttributes" value="{}" />
            <f:variable name="spacer" value="0" />
          </f:if>

          <li aria-current="page" class="{itemClass}">
            <gdmcom:dynamicTag
              as="{labelTag}"
              class="{labelClass}"
              additionalAttributes="{labelTagAttributes}"
            >
              <f:if condition="{iterator.isFirst}">
                <f:then>
                  <gdmcomc:icon
                    name="default/home"
                    class="w-5 h-5 hover:text-primary-500"
                  />
                </f:then>
                <f:else>{item.title}</f:else>
              </f:if>
            </gdmcom:dynamicTag>
            <f:if condition="{spacer}">
              <gdmcomc:icon
                name="default/angle-right"
                class="w-3 h-3 text-gray-600"
              />
            </f:if>
          </li>
        </f:for>
      </ol>
    </nav>
  </f:section>
</html>
