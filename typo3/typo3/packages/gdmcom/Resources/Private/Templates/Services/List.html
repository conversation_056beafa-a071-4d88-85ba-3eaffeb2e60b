<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/vhs/ViewHelpers"
  xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-show="http://example.org/dummy-ns"
>
  <f:layout name="DefaultPlugin" />

  <f:section name="Form">
    <f:if condition="{options -> f:count()} > 1">
      <form x-bind="serviceFilterBinding">
        <gdmcomc:pilltabs class="col-span-full mb-8 mt-4" tabs="{options}" />
      </form>
    </f:if>
  </f:section>

  <f:section name="Item">
    <f:variable
      name="categoriesArray"
      value="{gdmcom:split(value: service.companyIdStr, separator: ',')}"
    />

    <f:variable
      name="itemData"
      value="{
        itemCycle: iterator.cycle,
        categories: categoriesArray
      }"
    />

    <f:variable
      name="itemJsonData"
      value="{itemData -> v:format.json.encode()}"
    />
    <f:variable name="additionalAttributes" value="{x-data: itemJsonData}" />

    <div x-data="{itemJsonData}" x-bind="serviceItemBinding" class="md:flex">
      <f:if condition="{service.link}">
        <f:then>
          <gdmcomc:textlink link="{service.link}">
            <f:render section="Card" arguments="{_all}" />
          </gdmcomc:textlink>
        </f:then>
        <f:else>
          <f:render section="Card" arguments="{_all}" />
        </f:else>
      </f:if>
    </div>
  </f:section>

  <f:section name="Card">
    <gdmcomc:content.card
      header="{
          headline: service.header,
          layout: 106
        }"
      icon="services/{service.icon}"
      badge="{hideBadge ? '' : service.serviceCategoryStr}"
      class="group/jobcard"
    >
      {service.bodytext -> f:format.html()}
    </gdmcomc:content.card>
  </f:section>

  <f:section name="Main">
    <f:variable name="mobileItemLimit" value="10" />
    <f:variable name="lengthOfRecords" value="{serviceRecords -> f:count()}" />

    <f:variable
      name="listModuleData"
      value="{
        limit: mobileItemLimit,
        length: lengthOfRecords,
        type: 'service'
      }"
    />

    <div
      x-data="list({listModuleData -> v:format.json.encode()})"
      class="flex flex-col gap-4"
    >
      <f:render
        section="Form"
        arguments="{
        options: companyCategories,
      }"
      />

      <div
        class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8"
        x-ref="list"
      >
        <f:for each="{serviceRecords}" as="service" iteration="iterator">
          <f:render
            section="Item"
            arguments="{
            service: service,
            iterator: iterator,
            categoriesArray: service.companyIdStr
          }"
          />
        </f:for>
      </div>

      <f:if condition="{lengthOfRecords} > {mobileItemLimit}">
        <button
          type="button"
          x-on:click="showMore"
          class="md:hidden w-fit px-5 py-2.5 mt-4 self-center bg-primary-500 hover:bg-primary-200 inline-flex items-center hover:underline font-bold justify-between gap-x-2 focus-visible:outline-none focus:ring-4 focus:ring-secondary-500 rounded-lg dark:bg-primary-300 dark:hover:bg-primary-500 dark:focus:bg-primary-500 dark:text-black"
          x-show="showButton"
        >
          Mehr anzeigen
          <gdmcomc:icon name="default/arrow-right" class="size-4" />
        </button>
      </f:if>
    </div>
  </f:section>
</html>
