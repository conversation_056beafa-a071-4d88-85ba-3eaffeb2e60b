<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  data-namespace-typo3-fluid="true"
>
  <f:layout name="Default" />

  <f:section name="SvgItem">
    <gdmcomc:svg asset="{item}" class="w-full h-9 md:h-12" />
  </f:section>

  <f:section name="ItemLink">
    <f:link.typolink
      parameter="{item.link}"
      target="_blank"
      title="{item.title}"
      class="{class}"
    >
      <f:render section="SvgItem" arguments="{item:item}" />
    </f:link.typolink>
  </f:section>

  <f:section name="Item">
    <f:variable
      name="itemClass"
      value="flex justify-center border border-gray-200 p-3 md:px-8 md:py-6 transition-saturate duration-150 ease-in saturate-0 hover:saturate-100 hover:shadow-md rounded-lg bg-white basis-[calc(50%-1rem)] xl:basis-[calc(25%-1.5rem)] dark:bg-gray-300 dark:border-gray-600"
    />
    <f:if condition="{item.link}">
      <f:then>
        <f:render
          section="ItemLink"
          arguments="{
          item: item,
          class: itemClass
        }"
        />
      </f:then>
      <f:else>
        <div class="{itemClass}">
          <f:render section="SvgItem" arguments="{item:item}" />
        </div>
      </f:else>
    </f:if>
  </f:section>

  <f:section name="Main">
    <div class="flex flex-wrap justify-center gap-8">
      <f:for each="{data.assets}" as="item">
        <f:render section="Item" arguments="{item: item}" />
      </f:for>
    </div>
  </f:section>
</html>
