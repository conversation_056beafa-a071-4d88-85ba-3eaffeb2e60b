<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  data-namespace-typo3-fluid="true"
>
  <f:layout name="Default" />

  <f:section name="Main">
    <f:variable name="numbersOfColumns" value="1" />

    <f:variable name="assetAlignmentClass" value="" />

    <f:variable name="textAligmentClass" value="" />

    <f:switch expression="{data.tx_mask_layout}">
      <f:case value="1">
        <f:variable name="numbersOfColumns" value="2" />
      </f:case>
      <f:case value="2">
        <f:variable name="assetAlignmentClass" value="@xl/column:order-2" />

        <f:variable name="textAligmentClass" value="@xl/column:order-1" />
      </f:case>
      <f:case value="3">
        <f:variable name="assetAlignmentClass" value="@xl/column:order-2" />

        <f:variable name="textAligmentClass" value="@xl/column:order-1" />

        <f:variable name="numbersOfColumns" value="2" />
      </f:case>
    </f:switch>
    <f:variable name="entryPointClasses" value="group/box" />

    <f:if condition="{data.assets}">
      <f:variable
        name="entryPointClasses"
        value="{entryPointClasses} grid grid-cols-1 @xl/column:grid-cols-2 gap-8 @lg/column:gap-12 @xl/column:gap-16"
      />

      <f:switch expression="{data.tx_mask_vertical_alignment}">
        <f:case value="0">
          <f:variable
            name="textAligmentClass"
            value="{textAligmentClass} self-center"
          />
        </f:case>
        <f:case value="2">
          <f:variable
            name="textAligmentClass"
            value="{textAligmentClass} self-end"
          />
        </f:case>
      </f:switch>
    </f:if>

    <div class="{entryPointClasses}">
      <f:if condition="{data.assets}">
        <f:if condition="{data.assets -> f:count()} == 1">
          <f:then>
            <f:variable name="cropVariant" value="default" />
          </f:then>
          <f:else>
            <f:variable name="cropVariant" value="standard-portrait" />
          </f:else>
        </f:if>
        <f:comment> ID of FILETYPE_IMAGE: 2 ID of FILETYPE_VIDEO: 4 </f:comment>
        <f:if condition="{data.assets.0.type} == 4">
          <f:then>
            <gdmcomc:content.video
              video="{data.assets}"
              class="{assetAlignmentClass}"
            />
          </f:then>
          <f:else>
            <gdmcomc:imageSet
              images="{data.assets}"
              cropVariant="{cropVariant}"
              imageClasses="rounded-lg"
              figureStyle="--image-top-offset: calc(1.5rem * {iterator.index})"
              class="{assetAlignmentClass}"
            />
          </f:else>
        </f:if>
      </f:if>
      <gdmcomc:content.snippet
        header="{
          headline: data.header,
          layout: data.header_layout
        }"
        logo="{
          asset: data.tx_mask_logo,
          projectName: 'gdmcom'
        }"
        text-columns="{numbersOfColumns}"
        class="{textAligmentClass}"
      >
        {data.bodytext -> f:format.html()}
        <fc:content slot="cta">
          <f:if condition="{data.tx_mask_link}">
            <gdmcomc:button link="{data.tx_mask_link}" theme="primary" />
          </f:if>
        </fc:content>
      </gdmcomc:content.snippet>
    </div>
  </f:section>
</html>
