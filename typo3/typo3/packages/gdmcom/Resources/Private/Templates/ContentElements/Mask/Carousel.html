<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
    data-namespace-typo3-fluid="true"
	>
  <f:layout name="Default" />

  <f:section name="Item">
    <div class="grid flex-none snap-start {layoutClass} px-4">
      <f:cObject typoscriptObjectPath="lib.tx_mask.content">
        {item.uid}
      </f:cObject>
    </div>
  </f:section>

  <f:section name="Canvas">
    <f:for each="{items}" as="item">
      <f:render section="Item" arguments="{
        item: item,
        layoutClass: itemLayoutClass
      }" />
    </f:for>
  </f:section>

  <f:section name="Main">
    <f:switch expression="{data.tx_mask_layout}">
      <f:case value="4">
        <f:variable name="itemLayoutClass"  value="w-full lg:w-1/2 xl:w-1/4"/>
      </f:case>
      <f:case value="3">
        <f:variable name="itemLayoutClass"  value="w-full lg:w-1/2 xl:w-1/3"/>
      </f:case>
      <f:defaultCase>
        <f:variable name="itemLayoutClass"  value="w-full"/>
      </f:defaultCase>
    </f:switch>
    <f:variable
        name="carouselModuleData"
        value="{}"
    />
    <gdmcomc:carousel module-data="{carouselModuleData}">
      <f:render section="Canvas" arguments="{
        items: data.tx_mask_cards,
        itemLayoutClass: itemLayoutClass
      }" />
    </gdmcomc:carousel>
  </f:section>
</html>
