<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <f:layout name="Default" />

  <f:section name="Main">
    <ul class="flex flex-wrap gap-4">
      <f:for each="{data.tx_mask_icons}" as="item">
        <li
          class="flex text-gray-100 rounded focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-secondary-500"
        >
          <f:variable name="icon" value="{item.tx_mask_icon}" />
          <f:variable name="link" value="{item.tx_mask_link}" />
          <f:variable
            name="iconName"
            value="{icon -> v:format.pregReplace(pattern: '/.svg/', replacement: '')}"
          />
          <f:if condition="{link}">
            <f:link.typolink
              parameter="{link}"
              target="_blank"
              additionalAttributes="{aria-label: 'Go to {iconName} page'}"
            >
              <gdmcomc:icon
                name="default/social/{icon}"
                class="text-gray-100 size-6 hover:text-orange-500"
              />
            </f:link.typolink>
          </f:if>
        </li>
      </f:for>
    </ul>
  </f:section>
</html>
