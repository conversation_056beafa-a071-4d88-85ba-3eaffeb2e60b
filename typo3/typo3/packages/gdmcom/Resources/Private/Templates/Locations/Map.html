<html data-namespace-typo3-fluid="true"
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
    xmlns:v="http://typo3.org/ns/TYPO3/CMS/vhs/ViewHelpers"
    xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
    xmlns:x-on="http://example.org/dummy-ns"
    xmlns:x-bind="http://example.org/dummy-ns"
    xmlns:x-show="http://example.org/dummy-ns"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
>
  <f:layout name="DefaultPlugin" />

  <f:section name="Form">
    <f:if condition="{options -> f:count()} > 1">
      <form x-bind="locationFilterBinding">
        <gdmcomc:pilltabs class="mt-4 col-span-full"
          tabs="{options}" />
      </form>
    </f:if>
  </f:section>

  <f:section name="ContactLink">
    <gdmcomc:textlink
      icon-before="{icon}"
      link="{typolink}"
      class="inline-flex items-center font-bold hover:underline gap-x-2"
      default-title="{defaultTitle}"
    />
  </f:section>

  <f:section name="Item">
    <f:variable name="itemData"
      value="{
        uid: location.uid
      }" />

    <f:variable name="itemJsonData"
      value="{itemData -> v:format.json.encode()}" />
    <f:variable name="additionalAttributes"
      value="{x-data: itemJsonData}" />
    <f:variable name="locationAddress">{v:format.pregReplace(
        pattern: "/\,/",
        replacement: "<br/>",
        subject: location.address
      )}</f:variable>
    <div
      x-data="{itemJsonData}"
      x-bind="locationItemBinding"
      class="flex absolute top-0 right-0 lg:top-5 lg:bottom-5 lg:right-5 bottom-0 w-full lg:w-[320px] z-[1001]"
      x-cloak=""
    >
      <gdmcomc:content.card
        header="{
            headline: location.title,
            layout: 106
          }"
        logo="{
          asset: location.company.logo
          projectName: 'gdmcom'
        }"
        class="!border-gray-200 shadow-none outline outline-white outline-4 md:outline-none"
      >
        <fc:content slot="actions">
          <button
            class="inline-flex items-center w-full hover:underline gap-x-2"
            x-on:click="hideLocationCard"
          >
            <gdmcomc:icon name="default/arrow-left" class="size-4" />
            Details ausblenden
          </button>
        </fc:content>
        <fc:content>
          <div class="flex flex-col gap-4 leading-normal">
            {locationAddress -> f:format.html()}
            <f:if condition="{location.telephone} OR {location.email} OR {location.website}">
              <gdmcomc:headline
                headline="Kontakt:"
                layout="107"
                class="{modeClass} group-hover-[.flex]/jobcard:underline break-words mt-2"
              />
              <f:if condition="{location.telephone}">
                <f:render section="ContactLink" arguments="{
                  typolink: location.telephone,
                  class: '',
                  icon: 'default/phone',
                  defaultTitle: 'Anrufen'
                }" />
              </f:if>
              <f:if condition="{location.email}">
                <f:render section="ContactLink" arguments="{
                  typolink: location.email,
                  class: '',
                  icon: 'default/mail',
                  defaultTitle: 'E-Mail schreiben'
                }" />
              </f:if>
              <f:if condition="{location.website}">
                <f:render section="ContactLink" arguments="{
                  typolink: location.website,
                  class: '',
                  icon: 'default/globe',
                  defaultTitle: 'Zur Unternehmensseite'
                }" />
              </f:if>
            </f:if>
          </div>
        </fc:content>
      </gdmcomc:content.card>
    </div>
  </f:section>

  <f:section name="Main">
    <f:if condition="{settings.yellowmaps.scriptUrlWithApiKey}">
      <f:asset.script src="{settings.yellowmaps.scriptUrlWithApiKey}"
        priority="false"
        identifier="yellowmap"
      />

      <f:variable name="mapsData" value="{
        options: options,
        poiData: geoJson
      }" />
      <div x-data="maps({mapsData -> v:format.json.encode()})"
        x-cloak=""
        x-show="mapInitialized"
        class="relative overflow-hidden flex flex-col gap-8"
      >
        <f:render section="Form" arguments="{
          options: companyCategories
        }" />
        <div class="aspect-[2/3] lg:aspect-square xl:aspect-[2/1] relative rounded-lg overflow-hidden">
          <div x-ref="map" style="height: 100%;"></div>
          <f:for each="{locationRecords}" as="location" iteration="iterator">
            <f:render section="Item" arguments="{
              location: location,
              iterator: iterator,
            }" />
          </f:for>
        </div>
        <div class="absolute invisible">
          <f:render section="DefaultMarkerIcon" arguments="{}" />
          <f:render section="ActiveMarkerIcon" arguments="{}" />
        </div>
      </div>
    </f:if>

    <f:if condition="!{settings.yellowmaps.scriptUrlWithApiKey}">
      <div class="flex flex-col p-4 text-red-800 rounded-md bg-red-50">
        <span class="font-semibold">Keine API Script URL für Yellowmaps hinterlegt</span>
        <p class="font-normal">
          Bitte kontaktieren Sie einen Administrator der Ihnen bei dieser Sache weiterhilft
        </p>
      </div>
    </f:if>
  </f:section>

  <f:section name="ActiveMarkerIcon">
    <div x-ref="activeMarker">
      <svg viewBox="0 0 68 85" fill="none" class="text-primary-500">
        <g>
          <g>
            <ellipse cx="34" cy="67.5556" rx="5.44444" ry="2.33333" fill="black" fill-opacity="0.2"/>
          </g>
          <path d="M34 66L33.7478 66.296C33.8931 66.4198 34.1069 66.4198 34.2522 66.296L34 66ZM34 66C34.2522 66.296 34.2523 66.2959 34.2526 66.2957L34.2534 66.295L34.2565 66.2923L34.2685 66.2821L34.3146 66.2424C34.3321 66.2273 34.3532 66.209 34.3778 66.1875C34.4101 66.1595 34.4483 66.126 34.4924 66.0874C34.6477 65.951 34.8749 65.7495 35.1637 65.4874C35.7412 64.9634 36.5652 64.1973 37.5537 63.2272C39.53 61.2874 42.1659 58.5296 44.8028 55.2579C50.0598 48.7353 55.3889 40.0881 55.3889 31.7778C55.3889 19.5489 45.826 9.61111 34 9.61111C22.174 9.61111 12.6111 19.5489 12.6111 31.7778C12.6111 40.0881 17.9402 48.7353 23.1972 55.2579C25.8341 58.5296 28.47 61.2874 30.4463 63.2272C31.4348 64.1973 32.2588 64.9634 32.8363 65.4874C33.1251 65.7495 33.3523 65.951 33.5076 66.0874C33.5853 66.1556 33.645 66.2074 33.6854 66.2424L33.7315 66.2821L33.7435 66.2923L33.7466 66.295L33.7474 66.2957C33.7477 66.2959 33.7478 66.296 34 66Z" fill="white" stroke="#D4D4D4" stroke-width="0.777778" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M34 64.6669C33.9285 64.6027 33.8517 64.5333 33.7696 64.4589C33.2039 63.9455 32.3932 63.1919 31.4192 62.236C29.47 60.3228 26.873 57.6053 24.2786 54.3864C19.0466 47.8949 14 39.575 14 31.7778C14 20.2679 22.9883 11 34 11C45.0117 11 54 20.2679 54 31.7778C54 39.575 48.9534 47.8949 43.7214 54.3864C41.127 57.6053 38.53 60.3228 36.5808 62.236C35.6068 63.1919 34.7961 63.9455 34.2304 64.4589C34.1483 64.5333 34.0715 64.6027 34 64.6669Z" fill="currentColor" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M20.6013 30.0498C21.629 29.5936 22.9977 29.1419 24.6199 29.5383C24.6199 29.5383 28.7905 30.612 29.1914 30.7042C29.7998 30.8425 30.4495 31.0637 31.4634 31.0775C32.4772 31.0913 33.6063 30.6673 34.5603 30.3171C35.1409 30.1005 43.2103 27.0221 43.2103 27.0221C44.7771 26.4368 45.7956 26.5843 47.0629 26.9161C48.2611 27.2295 48.1643 27.5935 47.5837 27.8101C47.3671 27.8885 34.5556 32.7043 34.4266 32.7503C33.4681 33.1052 32.5602 33.4693 31.4818 33.4693C30.832 33.4693 29.8827 33.202 29.279 33.0637C29.1684 33.0361 20.9193 30.9991 20.6428 30.93C20.0391 30.7872 19.5829 30.5014 20.6013 30.0498Z" fill="#000000"/>
          <path d="M22.8825 33.4785C23.7305 33.1052 24.7029 32.9209 26.0439 33.2481C26.0439 33.2481 29.2514 34.0638 29.7767 34.179C30.2975 34.2942 30.9058 34.4877 31.7399 34.497C32.5787 34.5108 33.5096 34.1605 34.2976 33.8702C34.7769 33.6951 40.2885 31.5706 41.5559 31.1098C42.8232 30.6489 43.6619 30.6305 44.7172 30.8563C45.662 31.0591 45.6251 31.4139 45.1458 31.5936C44.9707 31.6582 34.3621 35.6444 34.2561 35.6813C33.4681 35.9716 32.7169 36.2758 31.8229 36.2758C31.2883 36.2758 30.5049 36.0546 30.0025 35.9394C29.9104 35.9163 23.1406 34.2527 22.9148 34.1974C22.4171 34.0776 22.0392 33.8426 22.8825 33.4739" fill="#000000"/>
          <path d="M25.3526 36.4786C26.0346 36.1744 26.7305 36.0362 27.8089 36.2942C27.8089 36.2942 30.0486 36.8519 30.4679 36.9486C30.8873 37.0408 31.4219 37.1837 32.0947 37.1929C32.7675 37.2021 33.5417 36.9348 34.1777 36.6998C34.5648 36.5569 38.9981 34.8518 40.0166 34.4785C41.035 34.1098 41.9199 34.1375 42.7724 34.3172C43.5328 34.4785 43.5973 34.7827 43.2102 34.9255C43.0673 34.9762 34.5694 38.2113 34.4865 38.2436C33.8505 38.4786 33.2468 38.7183 32.5279 38.7183C32.0947 38.7183 31.4679 38.5385 31.0624 38.451C30.9887 38.4325 25.5093 37.0684 25.3249 37.0224C24.924 36.9256 24.6705 36.7735 25.348 36.474" fill="#000000"/>
        </g>
      </svg>
    </div>
  </f:section>
  <f:section name="DefaultMarkerIcon">
    <div x-ref="defaultMarker">
      <svg viewBox="0 0 68 85" fill="none" class="hover:text-black text-white">
        <g>
          <g>
            <ellipse cx="34" cy="67.5556" rx="5.44444" ry="2.33333" fill="black" fill-opacity="0.2"/>
          </g>
          <path d="M34 66L33.7478 66.296C33.8931 66.4198 34.1069 66.4198 34.2522 66.296L34 66ZM34 66C34.2522 66.296 34.2523 66.2959 34.2526 66.2957L34.2534 66.295L34.2565 66.2923L34.2685 66.2821L34.3146 66.2424C34.3321 66.2273 34.3532 66.209 34.3778 66.1875C34.4101 66.1595 34.4483 66.126 34.4924 66.0874C34.6477 65.951 34.8749 65.7495 35.1637 65.4874C35.7412 64.9634 36.5652 64.1973 37.5537 63.2272C39.53 61.2874 42.1659 58.5296 44.8028 55.2579C50.0598 48.7353 55.3889 40.0881 55.3889 31.7778C55.3889 19.5489 45.826 9.61111 34 9.61111C22.174 9.61111 12.6111 19.5489 12.6111 31.7778C12.6111 40.0881 17.9402 48.7353 23.1972 55.2579C25.8341 58.5296 28.47 61.2874 30.4463 63.2272C31.4348 64.1973 32.2588 64.9634 32.8363 65.4874C33.1251 65.7495 33.3523 65.951 33.5076 66.0874C33.5853 66.1556 33.645 66.2074 33.6854 66.2424L33.7315 66.2821L33.7435 66.2923L33.7466 66.295L33.7474 66.2957C33.7477 66.2959 33.7478 66.296 34 66Z" fill="white" stroke="#D4D4D4" stroke-width="0.777778" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M34 64.6669C33.9285 64.6027 33.8517 64.5333 33.7696 64.4589C33.2039 63.9455 32.3932 63.1919 31.4192 62.236C29.47 60.3228 26.873 57.6053 24.2786 54.3864C19.0466 47.8949 14 39.575 14 31.7778C14 20.2679 22.9883 11 34 11C45.0117 11 54 20.2679 54 31.7778C54 39.575 48.9534 47.8949 43.7214 54.3864C41.127 57.6053 38.53 60.3228 36.5808 62.236C35.6068 63.1919 34.7961 63.9455 34.2304 64.4589C34.1483 64.5333 34.0715 64.6027 34 64.6669Z" fill="currentColor" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M20.6013 30.0498C21.629 29.5936 22.9977 29.1419 24.6199 29.5383C24.6199 29.5383 28.7905 30.612 29.1914 30.7042C29.7998 30.8425 30.4495 31.0637 31.4634 31.0775C32.4772 31.0913 33.6063 30.6673 34.5603 30.3171C35.1409 30.1005 43.2103 27.0221 43.2103 27.0221C44.7771 26.4368 45.7956 26.5843 47.0629 26.9161C48.2611 27.2295 48.1643 27.5935 47.5837 27.8101C47.3671 27.8885 34.5556 32.7043 34.4266 32.7503C33.4681 33.1052 32.5602 33.4693 31.4818 33.4693C30.832 33.4693 29.8827 33.202 29.279 33.0637C29.1684 33.0361 20.9193 30.9991 20.6428 30.93C20.0391 30.7872 19.5829 30.5014 20.6013 30.0498Z" fill="#FFDD00"/>
          <path d="M22.8825 33.4785C23.7305 33.1052 24.7029 32.9209 26.0439 33.2481C26.0439 33.2481 29.2514 34.0638 29.7767 34.179C30.2975 34.2942 30.9058 34.4877 31.7399 34.497C32.5787 34.5108 33.5096 34.1605 34.2976 33.8702C34.7769 33.6951 40.2885 31.5706 41.5559 31.1098C42.8232 30.6489 43.6619 30.6305 44.7172 30.8563C45.662 31.0591 45.6251 31.4139 45.1458 31.5936C44.9707 31.6582 34.3621 35.6444 34.2561 35.6813C33.4681 35.9716 32.7169 36.2758 31.8229 36.2758C31.2883 36.2758 30.5049 36.0546 30.0025 35.9394C29.9104 35.9163 23.1406 34.2527 22.9148 34.1974C22.4171 34.0776 22.0392 33.8426 22.8825 33.4739" fill="#F7A600"/>
          <path d="M25.3526 36.4786C26.0346 36.1744 26.7305 36.0362 27.8089 36.2942C27.8089 36.2942 30.0486 36.8519 30.4679 36.9486C30.8873 37.0408 31.4219 37.1837 32.0947 37.1929C32.7675 37.2021 33.5417 36.9348 34.1777 36.6998C34.5648 36.5569 38.9981 34.8518 40.0166 34.4785C41.035 34.1098 41.9199 34.1375 42.7724 34.3172C43.5328 34.4785 43.5973 34.7827 43.2102 34.9255C43.0673 34.9762 34.5694 38.2113 34.4865 38.2436C33.8505 38.4786 33.2468 38.7183 32.5279 38.7183C32.0947 38.7183 31.4679 38.5385 31.0624 38.451C30.9887 38.4325 25.5093 37.0684 25.3249 37.0224C24.924 36.9256 24.6705 36.7735 25.348 36.474" fill="#EC6608"/>
        </g>
      </svg>
    </div>
  </f:section>
</html>
