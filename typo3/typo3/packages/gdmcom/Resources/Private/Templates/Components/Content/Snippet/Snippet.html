<html
  data-namespace-typo3-fluid="true"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
>
  <fc:component>
    <fc:param name="header" type="Array" />
    <fc:param name="icon" type="string" optional="1" />
    <fc:param name="badge" type="string" optional="1" default="" />
    <fc:param
      name="logo"
      type="Array"
      optional="1"
      default="{
      asset: 0
    }"
    />
    <fc:param name="text-columns" type="integer" optional="1" default="1" />
    <fc:param name="cta" type="Slot" optional="1" />
    <fc:param name="orientation" type="integer" default="0" optional="1" />

    <f:variable
      name="textContainerClass"
      value="flex-1 lg:columns-{text-columns} lg:gap-x-4"
    />
    <f:variable
      name="logoClasses"
      value="group/product-card:flex group/product-card:items-center group/product-card:gap-2"
    />
    <f:variable name="textWrapperClass" value="flex-col gap-2.5" />

    <f:if condition="{text-columns} > 1">
      <f:variable
        name="textContainerClass"
        value="{textContainerClass} lg:block"
      />
    </f:if>

    <f:if condition="{orientation} == 1">
      <f:variable name="textWrapperClass" value="flex-col lg:flex-row gap-4" />
      <f:variable
        name="headlineColumnClass"
        value="lg:basis-[calc(33%-.25rem)]"
      />
    </f:if>

    <f:if condition="!{header.headline}">
      <f:variable
        name="textContainerClass"
        value="{textContainerClass} lg:group-[]/product-card:columns-3"
      />
    </f:if>

    <fc:renderer>
      <div
        class="group/snippet w-full text-base relative flex flex-col items-start group-[.text-center]/section-head:items-center gap-4 {class}"
      >
        <f:if condition="{badge} || {icon}">
          <div
            class="flex flex-row w-full {f:if(condition: icon, then: 'justify-between', else: 'justify-end' )}"
          >
            <f:if condition="{icon}">
              <gdmcomc:icon
                name="{icon}"
                class="mb-4 size-16 text-primary-500 dark:text-primary-300"
                as-shape="1"
              />
            </f:if>
            <f:if condition="{badge}">
              <gdmcomc:badge title="{badge}" class="w-auto h-fit" />
            </f:if>
          </div>
        </f:if>
        <f:if condition="{logo.asset}">
          <div
            class="w-full flex group-[.text-center]/section-head:justify-center gap-2 items-center text-3xl"
          >
            <gdmcomc:logo
              projectName="{logo.projectName}"
              name="{logo.asset}"
              class="w-auto h-4 group-[]/product-card:h-1em group-[]/product-card:w-auto group-[]/product-card:flex-none"
            />
            <f:if condition="{logo.text}">
              <span class="truncate font-bold">{logo.text}</span>
            </f:if>
          </div>
        </f:if>

        <div
          class="flex group-[.text-center]/section-head:text-red group-[.grid]/box:gap-x-16 group-[.grid]/box:gap-y-6 w-full {textWrapperClass}"
        >
          <f:if condition="{header.headline}">
            <div class="flex flex-col gap-1 {headlineColumnClass}">
              <f:if condition="{header.subline} AND {header.overline}">
                <p class="font-bold truncate text-gray-900 dark:text-white">
                  {header.subline}
                </p>
              </f:if>
              <gdmcomc:headline
                headline="{header.headline}"
                layout="{header.layout}"
                truncate="{header.truncate}"
                class="{modeClass} group-hover-[.flex]/jobcard:underline break-words"
              />
              <f:if condition="{header.subline} AND !{header.overline}">
                <p class="font-bold truncate text-gray-900 dark:text-white">
                  {header.subline}
                </p>
              </f:if>
            </div>
          </f:if>
          <div class="{textContainerClass}">
            <fc:slot />
          </div>
        </div>
        <fc:slot name="cta" />
      </div>
    </fc:renderer>
  </fc:component>
</html>
