<html
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/Vhs/ViewHelpers"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="textAligment" type="integer" optional="1" />
    <fc:param name="header" type="Array" optional="1" />
    <fc:param name="link" type="Typolink" optional="1" />
    <fc:param name="badge" type="string" optional="1" />
    <fc:param name="icon" type="string" optional="1" />
    <fc:param
      name="logo"
      type="Array"
      optional="1"
      default="{
      asset: 0
    }"
    />
    <fc:param
      name="button-theme"
      type="string"
      optional="1"
      default="secondary"
    />
    <fc:param name="image" type="Array" optional="1" />
    <fc:param name="actions" type="Slot" optional="1" />
    <fc:param name="footer" type="Slot" optional="1" />
    <fc:param name="orientation" type="integer" default="0" optional="1" />
    <fc:param name="marked-as" type="string" optional="1" />
    <fc:param name="text-columns" type="integer" optional="1" default="1" />
    <fc:param name="custom-border" type="string" optional="1" default="" />
    <fc:param name="custom-shadow" type="string" optional="1" default="" />

    <f:variable name="snippetAlignmentClass" value="" />
    <f:variable name="cropVariant" value="widescreen" />
    <f:variable name="directionClass" value="flex-col" />
    <f:variable name="cardClass" value="bg-white dark:bg-gray-800" />

    <f:variable
      name="borderClass"
      value="border border-gray-50 dark:border-gray-600"
    />
    <f:if condition="{custom-border}">
      <f:variable name="borderClass" value="border {custom-border}" />
    </f:if>
    <f:if condition="{marked-as}">
      <f:variable name="borderClass" value="border-2 border-primary-500" />
    </f:if>
    <f:variable name="shadowClass" value="shadow-lg" />
    <f:if condition="{custom-shadow}">
      <f:variable name="shadowClass" value="{custom-shadow}" />
    </f:if>

    <f:variable
      name="labelClass"
      value="flex items-center w-full leading-8 px-8 font-bold bg-primary-500 dark:text-black"
    />
    <f:if condition="{textAligment} == 2">
      <f:variable name="displayClass" value="lg:flex-row" />
      <f:variable name="snippetAlignmentClass" value="lg:justify-center" />
      <f:variable name="cropVariant" value="default" />
    </f:if>
    <f:if condition="{orientation} == 1">
      <f:variable name="directionClass" value="flex-col 2xl:flex-row" />
    </f:if>

    <f:variable name="heightClass" value="h-full" />
    <f:if condition="{marked-as}">
      <f:variable
        name="cardClass"
        value="bg-primary-50 dark:bg-primary-900 border-2 border-primary-500"
      />
      <f:if condition="{orientation} == 0">
        <f:variable name="heightClass" value="md:h-[calc(100%+2rem)]" />
        <f:variable name="cardClass" value="{cardClass} md:-mt-8 md:top-0" />
      </f:if>

      <f:if condition="{orientation} == 1">
        <f:variable name="cardClass" value="{cardClass} relative pt-8" />
        <f:variable
          name="labelClass"
          value="{labelClass} absolute top-0 h-8 flex-none"
        />
      </f:if>
    </f:if>

    <fc:renderer>
      <div
        class="group/card flex {directionClass} w-full {heightClass} {displayClass} rounded-lg group-[.flex]/jobcard:shadow-sm overflow-hidden focus:outline-none focus-visible:ring-4 focus-visible:ring-primary-600 active:outline-none dark:focus-visible:ring-primary-200 {cardClass} {borderClass} {shadowClass} {class}"
        tabindex="0"
      >
        <f:if condition="{marked-as}">
          <div class="{labelClass}">{marked-as}</div>
        </f:if>
        <f:if condition="{actions}">
          <div
            class="flex items-center self-center w-[85%] p-6 pl-0 border-b border-gray-300"
          >
            <fc:slot name="actions" />
          </div>
        </f:if>

        <f:if condition="{image}">
          <gdmcomc:image image="{image}" class="w-full" />
        </f:if>
        <gdmcomc:content.snippet
          header="{header}"
          logo="{logo}"
          icon="{icon}"
          badge="{badge}"
          class="p-6 flex items-center w-full h-full group-[.flex]/card:items-start group-[.flex]/jobcard:py-4 {snippetAlignmentClass}"
          orientation="{orientation}"
          text-columns="{text-columns}"
        >
          <fc:slot />

          <fc:content slot="cta">
            <f:if condition="{link.uri}">
              <f:if condition="{textAligment}!=2">
                <f:variable name="ctaClass" value="last:mt-auto" />
              </f:if>
              <gdmcomc:button
                link="{link}"
                theme="{button-theme}"
                class="{ctaClass}"
                force-link-type-icon="1"
              />
            </f:if>
          </fc:content>
        </gdmcomc:content.snippet>

        <f:if condition="{footer}">
          <f:variable name="footerClass" value="bg-gray-50 dark:bg-gray-700" />
          <f:if condition="{marked-as}">
            <f:variable
              name="footerClass"
              value="bg-primary-100 dark:bg-primary-800"
            />
          </f:if>

          <div class="px-6 py-8 {footerClass} flex flex-col justify-center">
            <fc:slot name="footer" />
          </div>
        </f:if>
      </div>
    </fc:renderer>
  </fc:component>
</html>
