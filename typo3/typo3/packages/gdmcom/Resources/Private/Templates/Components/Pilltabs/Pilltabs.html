<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="tabs" type="Array" />
    <fc:param name="activeTabs" type="Array" optional="[]" />
    <fc:param name="name" type="string" optional="1" default="pillTabs" />

    <fc:renderer>
      <div class="flex flex-wrap gap-4 justify-center {class}">
        <f:for each="{tabs}" as="tab">
          <f:switch expression="{tab.title}">
            <f:case value="GDMcom GmbH">
              <f:variable name="logo" value="gdmcom" />
            </f:case>
            <f:case value="GDMcom | Bau GmbH">
              <f:variable name="logo" value="construction" />
            </f:case>
            <f:case value="GDMcom | Planung GmbH">
              <f:variable name="logo" value="planning" />
            </f:case>
            <f:case value="GIBY">
              <f:variable name="logo" value="giby" />
            </f:case>
          </f:switch>

          <f:variable
            name="showTabValue"
            value="{v:condition.iterator.contains(needle: tab.uid, haystack: activeTabs, then: 1, else: 0)}"
          />
          <gdmcomc:form.checkbox
            value="{tab.uid}"
            name="{tab.title}"
            checked="{showTabValue}"
            showInput="0"
            class="group/pilltab inline-flex border ring-1 ring-gray-300 rounded-lg p-3 bg-white has-[:checked]:bg-black has-[:checked]:text-white has-[:disabled]:opacity-25 cursor-pointer hover:ring hover:ring-gray-400 dark:bg-gray-800 dark:has-[:checked]:bg-gray-100"
          >
            <div class="w-full h-4 flex justify-center">
              <gdmcomc:logo
                name="{logo}"
                class="w-auto h-full group-has-[:checked]/pilltab:grayscale group-has-[:checked]/pilltab:brightness-0 group-has-[:checked]/pilltab:invert dark:group-has-[:checked]/pilltab:invert-0"
              />
            </div>
          </gdmcomc:form.checkbox>
        </f:for>
      </div>
    </fc:renderer>
  </fc:component>
</html>
