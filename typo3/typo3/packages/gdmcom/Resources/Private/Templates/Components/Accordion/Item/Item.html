<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="title" type="string" />

    <fc:renderer>
      <details
        class="w-full border border-gray-200 rounded-lg shadow-sm bg-gray-50 dark:bg-gray-800 dark:border-gray-600 group/details"
      >
        <summary
          class="flex items-center justify-between gap-1 p-5 font-bold leading-6 text-gray-900 list-none cursor-pointer select-none dark:text-white"
        >
          {title}
          <gdmcomc:icon
            name="default/angle-down"
            class="flex-none w-5 h-5 group-open/details:rotate-180"
          />
        </summary>
        <div
          class="p-5 bg-white border-gray-200 rounded-b-lg border-t-1 dark:bg-gray-900 dark:border-gray-600"
        >
          <fc:slot name="content" />
        </div>
      </details>
    </fc:renderer>
  </fc:component>
</html>
