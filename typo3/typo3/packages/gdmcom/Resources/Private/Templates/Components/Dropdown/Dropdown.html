<html
  data-namespace-typo3-fluid="true"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-show="http://example.org/dummy-ns"
  xmlns:x-cloak="http://example.org/dummy-ns"
>
  <fc:component>
    <fc:param name="label" type="string" />
    <fc:param
      name="icon"
      type="string"
      optional="1"
      default="default/home.svg"
    />
    <fc:param name="name" type="string" optional="1" />

    <fc:param name="options" type="Array" optional="1" />
    <fc:param name="selected" type="Array" optional="1" />

    <fc:renderer>
      <div
        x-data="{ open: false }"
        x-on:click.outside="open = false"
        class="relative text-left {class}"
      >
        <button
          x-on:click="open = !open"
          class="w-full border hover:ring-2 hover:ring-gray-300 border-gray-300 rounded-lg px-5 py-3 text-center inline-flex gap-2 items-center transition-shadow duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-300 dark:bg-gray-800"
          x-bind:class="{ 'ring-2 ring-gray-300': open }"
          type="button"
        >
          <gdmcomc:icon name="{icon}" class="w-5 h-5" />
          <p class="mr-12 flex-1 text-left">{label}</p>
          <span
            class="transition-transform duration-100 ease-in-out"
            x-bind:class="{ 'rotate-180': open }"
          >
            <gdmcomc:icon name="default/chevron-down.svg" class="w-5 h-5" />
          </span>
        </button>
        <div
          x-show="open"
          class="z-10 absolute w-full bg-white border border-gray-100 rounded-lg shadow-lg mt-2 dark:bg-gray-700"
          x-cloak=""
          role="menu"
        >
          <ul class="p-3 space-y-3 text-sm text-gray-900">
            <f:for each="{options}" as="item">
              <li>
                <f:variable name="checked" value="0" />
                <v:condition.iterator.contains
                  needle="{item.uid}"
                  haystack="{selected}"
                >
                  <f:variable name="checked" value="1" />
                </v:condition.iterator.contains>

                <gdmcomc:form.checkbox
                  label="{item.title} ({item.count})"
                  name="{name}"
                  value="{item.uid}"
                  checked="{checked}"
                />
              </li>
            </f:for>
            <button
              x-on:click="$dispatch('reset-filter', '{name}')"
              class="flex items-center w-full gap-2 pt-3 text-sm font-medium border-t border-gray-200 rounded-b-lg hover:underline dark:text-white"
            >
              <gdmcomc:icon name="default/trash-bin" class="size-4" />
              zurücksetzen
            </button>
          </ul>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
