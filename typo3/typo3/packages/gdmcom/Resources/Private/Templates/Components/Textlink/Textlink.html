<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="link" type="Typolink" />
    <fc:param name="icon-before" type="string" optional="1" />
    <fc:param name="icon-after" type="string" optional="1" />
    <fc:param
      name="additionalAttributes"
      type="Array"
      optional="1"
      default=""
    />
    <fc:param name="default-title" type="string" optional="1" default="hier" />
    <fc:param
      name="force-link-type-icon"
      type="boolean"
      optional="1"
      default="0"
    />

    <f:if condition="{force-link-type-icon} == 1">
      <f:switch expression="{link.originalLink.type}">
        <f:case value="file">
          <f:variable
            name="icon-before"
            value="default/arrow-down-to-bracket"
          />
          <f:variable name="icon-after" value="" />
        </f:case>
        <f:case value="email">
          <f:variable name="icon-before" value="default/mail" />
          <f:variable name="icon-after" value="" />
        </f:case>
        <f:case value="telephone">
          <f:variable name="icon-before" value="default/phone" />
          <f:variable name="icon-after" value="" />
        </f:case>
      </f:switch>
    </f:if>

    <fc:renderer>
      <f:variable
        name="defaultAttributes"
        value="{
            'href': link.uri,
            'target': link.target,
          }"
      />
      <f:variable
        name="attributes"
        value="{v:iterator.merge(a: defaultAttributes, b: additionalAttributes)}"
      />
      <gdmcom:dynamicTag
        as="a"
        additionalAttributes="{attributes}"
        class="{link.class} {class}"
      >
        <f:if condition="{icon-before}">
          <gdmcomc:icon name="{icon-before}" class="size-4" />
        </f:if>
        <f:if condition="{content}">
          <f:then>
            <fc:slot />
          </f:then>
          <f:else>
            <f:if condition="{link.title}">
              <f:then> {link.title} </f:then>
              <f:else> {default-title} </f:else>
            </f:if>
          </f:else>
        </f:if>
        <f:if condition="{icon-after}">
          <gdmcomc:icon name="{icon-after}" class="size-4" />
        </f:if>
      </gdmcom:dynamicTag>
    </fc:renderer>
  </fc:component>
</html>
