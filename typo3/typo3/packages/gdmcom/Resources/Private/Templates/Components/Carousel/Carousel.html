<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-ref="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="module-data" type="Array" />
    <fc:renderer>
      <div
        x-data="carousel({module-data -> v:format.json.encode()})"
        class="flex flex-col gap-4"
      >
        <div class="-mx-2 -mt-2 overflow-hidden">
          <div
            x-ref="canvas"
            class="flex px-2 pt-2 pb-4 -mx-4 overflow-x-auto snap-mandatory snap-x scrollbar-hide"
          >
            <fc:slot />
          </div>
        </div>
        <div
          class="flex items-center justify-center gap-x-6"
          x-cloak=""
          x-show="needsNavigation"
        >
          <button
            type="button"
            x-bind:disabled="activeIndex === 0"
            class="inline-flex items-center justify-center rounded-full disabled:opacity-55 group-hover:bg-gray-900 group-focus:ring-4 group-focus:ring-white group-focus:outline-none"
            x-on:click="prev"
          >
            <gdmcomc:icon name="default/arrow-left" class="size-8" />
            <span class="sr-only">Previous</span>
          </button>
          <div class="flex gap-3 justify-center flex-wrap">
            <template x-for="(indicator, index) in slides">
              <button
                type="button"
                class="w-4 h-4 bg-gray-200 rounded-full dark:bg-gray-600"
                x-bind:class="{
                'bg-gray-900 dark:bg-white': activeIndex == index
              }"
                aria-current="true"
                aria-label="Slide {indicator}"
                x-on:click="slideTo(indicator)"
              ></button>
            </template>
          </div>
          <button
            type="button"
            x-bind:disabled="activeIndex === slides - 1"
            class="inline-flex items-center justify-center rounded-full disabled:opacity-55 group-hover:bg-gray-900 group-focus:ring-4 group-focus:ring-white group-focus:outline-none"
            x-on:click="next"
          >
            <gdmcomc:icon name="default/arrow-right" class="size-8" />
            <span class="sr-only">Next</span>
          </button>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
