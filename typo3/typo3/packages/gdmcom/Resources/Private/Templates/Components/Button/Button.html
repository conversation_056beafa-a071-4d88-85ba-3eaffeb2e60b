<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="size" type="string" optional="1" default="base" />
    <fc:param name="theme" type="string" optional="1" default="primary" />
    <fc:param name="link" type="Typolink" optional="1" />
    <fc:param name="icon-before" type="string" optional="1" />
    <fc:param
      name="icon-after"
      type="string"
      optional="1"
      default="default/arrow-right"
    />
    <fc:param name="additionalAttributes" type="Array" optional="1" />
    <fc:param name="type" type="string" optional="1" default="button" />
    <fc:param name="name" type="string" optional="1" />
    <fc:param name="value" type="string" optional="1" />
    <fc:param name="no-icon" type="integer" optional="1" default="0" />
    <fc:param name="default-title" type="string" optional="1" default="" />
    <fc:param name="placement" type="string" optional="1" default="between" />
    <fc:param
      name="force-link-type-icon"
      type="boolean"
      optional="1"
      default="0"
    />

    <fc:renderer>
      <f:variable name="placementClass" value="justify-between" />
      <f:if condition="{placement}=='center'">
        <f:variable name="placementClass" value="justify-center" />
      </f:if>
      <f:variable
        name="baseClass"
        value="inline-flex items-center hover:underline font-bold {placementClass} gap-x-2 focus-visible:outline-none focus:ring-4 focus:ring-secondary-500 rounded-lg dark:focus:ring-secondary-100 disabled:text-gray-700 dark:disabled:text-gray-400"
      />

      <f:variable name="themeClass" value="" />
      <f:switch expression="{theme}">
        <f:case value="primary">
          <f:variable
            name="themeClass"
            value="bg-primary-500 focus:bg-primary-200 hover:bg-primary-200 dark:bg-primary-300 dark:hover:bg-primary-500 dark:focus:bg-primary-500 dark:text-black"
          />
        </f:case>
        <f:case value="secondary">
          <f:variable
            name="themeClass"
            value="bg-white border border-primary-500 hover:border-primary-200 hover:bg-primary-200 dark:bg-gray-800 dark:text-white dark:hover:bg-primary-500 dark:hover:text-black dark:hover:border-primary-500 dark:focus:border-secondary-100"
          />
        </f:case>
      </f:switch>

      <f:variable name="sizeClass" value="" />
      <f:switch expression="{size}">
        <f:case value="xs">
          <f:variable name="sizeClass" value="px-3 py-1.5" />
        </f:case>
        <f:case value="sm">
          <f:variable name="sizeClass" value="px-3 py-2" />
        </f:case>
        <f:case value="lg">
          <f:variable name="sizeClass" value="px-5 py-3" />
        </f:case>
        <f:case value="xl">
          <f:variable name="sizeClass" value="px-6 py-3.5" />
        </f:case>
        <f:defaultCase>
          <f:variable name="sizeClass" value="px-2.5 py-2.5" />
        </f:defaultCase>
      </f:switch>

      <f:if condition="{no-icon}">
        <f:variable name="icon-after" value="" />
        <f:variable name="icon-before" value="" />
      </f:if>

      <f:if condition="{link.uri} != ''">
        <f:then>
          <gdmcomc:textlink
            link="{link}"
            icon-after="{icon-after}"
            icon-before="{icon-before}"
            force-link-type-icon="{force-link-type-icon}"
            class="{baseClass} {sizeClass} {themeClass} {link.class} {class}"
          >
            <f:if condition="{content}">
              <f:then>
                <fc:slot />
              </f:then>
              <f:else>
                <f:if condition="{link.title}">
                  <f:then> {link.title} </f:then>
                  <f:else> {default-title} </f:else>
                </f:if>
              </f:else>
            </f:if>
          </gdmcomc:textlink>
        </f:then>
        <f:else>
          <f:form.button
            type="{type}"
            name="{name}"
            value="{value}"
            additionalAttributes="{additionalAttributes}"
            class="{baseClass} {sizeClass} {themeClass} {link.class} {class}"
          >
            <f:if condition="{icon-before}">
              <gdmcomc:icon name="{icon-before}" class="size-4" />
            </f:if>
            <fc:slot />
            <f:if condition="{icon-after}">
              <gdmcomc:icon name="{icon-after}" class="size-4" />
            </f:if>
          </f:form.button>
        </f:else>
      </f:if>
    </fc:renderer>
  </fc:component>
</html>
