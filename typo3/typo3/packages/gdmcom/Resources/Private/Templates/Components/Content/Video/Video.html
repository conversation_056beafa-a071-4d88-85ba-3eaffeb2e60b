<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="video" type="Array" optional="1" />

    <fc:renderer>
      <f:for each="{video}" as="file">
        <f:if condition="{file.type} == 4">
          <f:variable name="poster" value="" />
          <f:if condition="{file.properties.poster}">
            <f:variable
              name="poster"
              value="{f:uri.image(src: '{file.properties.poster}', treatIdAsReference: 1)}"
            />
          </f:if>
          <div class="aspect-[16/9] {class}">
            <f:media
              file="{file}"
              additionalAttributes="{controls: 'controls', class: 'h-full w-full object-cover {class}', poster: '{poster}'}"
            />
          </div>
        </f:if>
      </f:for>
    </fc:renderer>
  </fc:component>
</html>
