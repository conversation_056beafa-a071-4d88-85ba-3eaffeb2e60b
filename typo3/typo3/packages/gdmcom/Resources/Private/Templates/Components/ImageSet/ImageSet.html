<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:gdmcomvh="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="images" type="Array" optional="1" />
    <fc:param name="figureStyle" type="string" optional="1" default="" />

    <f:variable name="cropJson" value="{image.0.properties.crop -> v:format.json.decode()}" />
    <f:variable name="focusX" value="{cropJson.default.focusArea.x}" />
    <f:variable name="focusY" value="{cropJson.default.focusArea.y}" />

    <f:variable name="objectPosition" value="{gdmcomvh:imageFocusPosition(focusX: focusX, focusY: focusY)}" />

    <fc:param name="imageClasses" type="string" optional="1" />
    <fc:param name="cropVariant" type="string" optional="1" default="widescreen"/>
    <fc:param name="aspectRatioClass" type="string" optional="1"/>

    <f:if condition="!{aspectRatioClass}">
      <f:switch expression="{cropVariant}">
        <f:case value="widescreen">
          <f:variable name="aspectRatioClass"
            value="aspect-[16/9]"/>
        </f:case>
         <f:case value="widescreen-portrait">
          <f:variable name="aspectRatioClass"
            value="aspect-[9/16]"/>
        </f:case>
        <f:case value="default">
          <f:variable name="aspectRatioClass"
            value="aspect-[4/3]"/>
        </f:case>
        <f:case value="standard-portrait">
          <f:variable name="aspectRatioClass"
            value="aspect-[3/4]"/>
        </f:case>
        <f:case value="landscape">
          <f:variable name="aspectRatioClass"
            value="aspect-[2/1]"/>
        </f:case>
        <f:case value="landscape-portrait">
          <f:variable name="aspectRatioClass"
            value="aspect-[1/2]"/>
        </f:case>
      </f:switch>
    </f:if>
    <f:variable name="imageContainerClass" value="{class}"/>
    <f:if condition="{images -> f:count()} > 1">
      <f:variable name="imageContainerClass" value="grid grid-cols-2 gap-x-6 mb-6 xl:mb-0 {class} "/>
    </f:if>

    <fc:renderer>
      <div class="{imageContainerClass}">
        <f:for each="{images}" as="image" iteration="iterator">
          <f:if condition="2 > {iterator.index}">
            <f:variable name="figureClass" value="{aspectRatioClass} size-full" />
            <f:if condition="!{iterator.isFirst}">
              <f:variable name="figureClass"
                value="{figureClass} mt-[var(--image-top-offset)]"
                />
            </f:if>
            <f:variable name="imageArray" value="{0: image}" />
            <gdmcomc:image
              image="{imageArray}"
              figureClass="{figureClass}"
              figureStyle="--image-top-offset: calc(1.5rem * {iterator.index})"
              imageClass="{imageClasses}"
            >
            </gdmcomc:image>
          </f:if>
        </f:for>
      </div>
    </fc:renderer>
  </fc:component>
</html>
