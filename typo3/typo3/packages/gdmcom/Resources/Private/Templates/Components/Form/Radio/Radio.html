<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:x-bind="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
>
    <fc:component>

        <fc:param name="value" type="string" />
        <fc:param name="name" type="string" />
        <fc:param name="checked" type="boolean" optional="1" default="0"/>
        <fc:param name="disabled" type="boolean" optional="1" default="0"/>
        <fc:param name="showInput" type="boolean" optional="1" default="1" />

        <fc:renderer>
            <f:if condition="!{showInput}">
                <f:variable name="hiddenClass" value="hidden" />
            </f:if>
            <f:variable name="randomId" value="{v:system.uniqId()}"/>
            <f:variable name="radioModuleData" value="{
                checked: checked,
                disabled: disabled,
            }" />

            <label
                x-data="radio({radioModuleData -> v:format.json.encode()})"
                for="radio-{randomId}"
                class="text-sm has-[:disabled]:text-gray-400 leading-none has-[:invalid]:text-red-500 {class}"
            >
                <input
                    type="radio"
                    id="radio-{randomId}"
                    name="{name}"
                    value="{value}"
                    x-bind:checked="checked"
                    x-bind:disabled="disabled"
                    x-bind="input"
                    x-ref="input"
                    class="{hiddenClass}"
                />
                    <fc:slot />
            </label>

        </fc:renderer>
    </fc:component>
</html>
