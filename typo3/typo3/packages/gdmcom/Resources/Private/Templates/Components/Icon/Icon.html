<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="name" type="string" />
    <fc:param name="as-shape" type="integer" optional="1" default="0" />
    <fc:param name="projectName" type="string" optional="1" default="gdmcom" />
    <fc:param name="spriteMapName" type="string" optional="1" default="icons" />

    <f:variable name="path" value="{f:split(value: name, separator: '/')}" />

    <!-- Set up path to the proper spritemap -->
    <f:variable
      name="spritemapPath"
      value="build/{projectName}/{path.0}-{spriteMapName}-spritemap.svg"
    />

    <!-- path array length -->
    <f:variable name="arrayLength" value="{f:count(subject: path)}" />

    <!-- last element of path array -->
    <f:variable name="lastIndex" value="{arrayLength - 1}" />

    <!-- Get the last element of the path array as iconName and remove .svg from it -->
    <f:variable
      name="iconName"
      value="{path.{lastIndex} -> v:format.pregReplace(pattern: '/.svg/', replacement: '')}"
    />

    <!-- Icon can not be directly in Icons folder. Needs to be in subfolder of Icons -->
    <f:if condition="{arrayLength} > 1">
      <f:then>
        <fc:renderer>
          <f:if condition="{as-shape} == 1">
            <f:then>
              <div
                class="bg-primary-50 rounded-xl inline-flex justify-center dark:bg-primary-800 {class} items-center"
              >
                <encore:svg
                  class="size-[66%]"
                  src="{spritemapPath}"
                  name="sprite-{iconName -> f:format.trim()}"
                  cacheBusterParameter="t"
                />
              </div>
            </f:then>
            <f:else>
              <encore:svg
                class="{class}"
                src="{spritemapPath}"
                name="sprite-{iconName -> f:format.trim()}"
                cacheBusterParameter="t"
              />
            </f:else>
          </f:if>
        </fc:renderer>
      </f:then>
    </f:if>
  </fc:component>
</html>
