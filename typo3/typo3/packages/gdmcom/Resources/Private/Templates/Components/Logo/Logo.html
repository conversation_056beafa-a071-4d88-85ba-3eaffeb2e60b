<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="name" type="string" optional="1" default="0" />
    <fc:param name="projectName" type="string" optional="1" default="gdmcom" />
    <fc:param
      name="spriteMapName"
      type="string"
      optional="1"
      default="logos-spritemap"
    />
    <f:variable
      name="spritemapPath"
      value="build/{projectName}/{projectName}-{spriteMapName}.svg"
    />
    <f:variable
      name="darkModeClasses"
      value="dark:grayscale dark:brightness-0 dark:invert"
    />
    <f:if condition="{name} == 'waipu'">
      <f:variable name="darkModeClasses" value="" />
    </f:if>

    <fc:renderer>
      <f:if condition="{name}">
        <encore:svg
          class="{class} {darkModeClasses}"
          src="{spritemapPath}"
          name="sprite-{name}"
          cacheBusterParameter="t"
          inline="true"
        />
      </f:if>
    </fc:renderer>
  </fc:component>
</html>
