<html
  data-namespace-typo3-fluid="true"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
>
  <fc:component>
    <fc:param name="label" type="string" optional="1" />
    <fc:param name="value" type="string" />
    <fc:param name="name" type="string" />
    <fc:param name="checked" type="boolean" optional="1" default="0" />
    <fc:param name="showInput" type="boolean" optional="1" default="1" />

    <fc:renderer>
      <f:if condition="!{showInput}">
        <f:variable name="hiddenClass" value="hidden" />
      </f:if>
      <f:variable name="randomId" value="{v:system.uniqId()}" />
      <f:variable
        name="checkboxModuleData"
        value="{
        checked: checked
      }"
      />

      <label
        x-data="checkbox({checkboxModuleData -> v:format.json.encode()})"
        for="checkbox-{randomId}"
        class="relative text-sm has-[:disabled]:text-gray-600 leading-none has-[:invalid]:text-red-500 dark:text-white dark:has-[:disabled]:text-gray-500 {class}"
      >
        <input
          type="checkbox"
          name="{name}"
          id="checkbox-{randomId}"
          value="{value}"
          x-bind:checked="checked"
          x-bind="input"
          x-ref="input"
          class="{hiddenClass} peer/checkbox bg-gray-50 border-gray-300 rounded focus:text-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 checked:text-primary-500 checked:border-primary-300 dark:text-primary-300 checked:bg-none dark:checked:bg-primary-300"
        />
        <f:if condition="{showInput}">
          <gdmcomc:icon
            name="default/check"
            class="absolute hidden w-4 h-4 top-[1px] peer-checked/checkbox:block dark:text-black"
          />
        </f:if>
        <f:if condition="{content}">
          <f:then>
            <fc:slot />
          </f:then>
          <f:else> {label} </f:else>
        </f:if>
      </label>
    </fc:renderer>
  </fc:component>
</html>
