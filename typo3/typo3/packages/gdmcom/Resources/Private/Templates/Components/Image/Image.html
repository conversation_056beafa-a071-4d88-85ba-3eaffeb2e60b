<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:gdmcomvh="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="image" type="Array" optional="1" />
    <fc:param name="figureStyle" type="string" optional="1" default="" />
    <fc:param name="figureClass" type="string" optional="1" default="" />
    <fc:param name="imageClass" type="string" optional="1" default="" />
    <f:variable name="cropJson" value="{image.0.properties.crop -> v:format.json.decode()}" />
    <f:variable name="focusX" value="{cropJson.default.focusArea.x}" />
    <f:variable name="focusY" value="{cropJson.default.focusArea.y}" />

    <f:variable name="objectPosition" value="{gdmcomvh:imageFocusPosition(focusX: focusX, focusY: focusY)}" />

    <fc:renderer>
      <figure
        style="{figureStyle}"
        class="{figureClass} {class}"
      >
        <f:image
          image="{image.0}"
          width="1280"
          crop="{image.0.properties.crop}"
          alt="{image.0.properties.alternative}"
          class="w-full h-full object-cover {objectPosition} {imageClass}"
        />
        <f:if condition="{image.description}">
          <figcaption class="text-gray-900 font-base p-2">
            {image.description}
          </figcaption>
        </f:if>
      </figure>
    </fc:renderer>
  </fc:component>
</html>
