<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="grid-type-class" type="string" default="" optional="1" />
    <fc:param name="spacing-top" type="string" default="lg" optional="1" />
    <fc:param name="spacing-bottom" type="string" default="lg" optional="1" />
    <fc:param name="columns" type="Array" />
    <fc:param name="head" type="Array" />
    <fc:param name="background" type="string" />
    <fc:param name="centered" type="integer" optional="1" default="0" />

    <fc:renderer>
      <f:switch expression="{background}">
        <f:case value="primary-light">
          <f:variable
            name="backgroundClasses"
            value="bg-primary-50 dark:bg-primary-800"
          />
        </f:case>
        <f:case value="primary">
          <f:variable
            name="backgroundClasses"
            value="bg-primary-500 dark:bg-primary-600"
          />
          <f:variable name="ctaTheme" value="primary-dark" />
        </f:case>
        <f:case value="light-gray">
          <f:variable
            name="backgroundClasses"
            value="bg-gray-50 dark:bg-gray-800"
          />
        </f:case>
        <f:defaultCase>
          <f:variable name="backgroundClasses" value="" />
        </f:defaultCase>
      </f:switch>

      <f:variable name="spacingClasses" value="" />
      <f:switch expression="{spacing-top}">
        <f:case value="lg">
          <f:variable
            name="spacingClasses"
            value="{spacingClasses} pt-12 lg:pt-16 xl:pt-24"
          />
        </f:case>
        <f:case value="sm">
          <f:variable
            name="spacingClasses"
            value="{spacingClasses} pt-3 lg:pt-4"
          />
        </f:case>
        <f:case value="base">
          <f:variable
            name="spacingClasses"
            value="{spacingClasses} pt-6 lg:pt-8 xl:pt-12"
          />
        </f:case>
      </f:switch>
      <f:switch expression="{spacing-bottom}">
        <f:case value="lg">
          <f:variable
            name="spacingClasses"
            value="{spacingClasses} pb-12 lg:pb-16 xl:pb-24"
          />
        </f:case>
        <f:case value="sm">
          <f:variable
            name="spacingClasses"
            value="{spacingClasses} pb-3 lg:pb-4"
          />
        </f:case>
        <f:case value="base">
          <f:variable
            name="spacingClasses"
            value="{spacingClasses} pb-6 lg:pb-8 xl:pb-12"
          />
        </f:case>
      </f:switch>

      <f:variable
        name="max-w-container-class"
        value="max-w-container mx-auto"
      />
      <f:if condition="{centered}==1">
        <f:variable
          name="grid-type-class"
          value="{grid-type-class} {max-w-container-class}"
        />
      </f:if>

      <div
        class="group/section {backgroundClasses} {spacingClasses} {class} px-content bleed-content"
      >
        <fc:slot />

        <f:if condition="{head.uid}">
          <div
            class="group/section-head mb-16 text-center {max-w-container-class}"
          >
            <f:cObject typoscriptObjectPath="lib.tx_mask.content">
              {head.uid}
            </f:cObject>
          </div>
        </f:if>

        <gdmcomc:container.grid columns="{columns}" class="{grid-type-class}" />
      </div>
    </fc:renderer>
  </fc:component>
</html>
