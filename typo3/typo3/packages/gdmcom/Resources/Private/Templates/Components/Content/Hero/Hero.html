<html
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/Vhs/ViewHelpers"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="header" type="string" optional="1" />
    <fc:param name="image" type="Array" optional="1" />
    <fc:param name="description" type="string" optional="1" />

    <fc:renderer>
      <div class="flex flex-col xl:flex-row bleed-content">
        <div class="lg:basis-1/2">
          <f:if condition="{image}">
            <gdmcomc:image
              image="{image}"
              class="w-full h-full aspect-[2/1] xl:aspect-[3/4] 2xl:aspect-[4/3]"
            />
          </f:if>
        </div>
        <div
          class="relative bg-primary-500 px-6 pt-6 pb-20 lg:px-8 lg:pt-8 xl:px-16 xl:pt-20 xl:pb-32 flex flex-col gap-6 lg:gap-8 xl:gap-10 lg:basis-1/2 dark:bg-primary-700"
        >
          <f:if condition="{header}">
            <gdmcomc:headline headline="{header}" layout="hero" />
          </f:if>
          <div
            class="border-l-4 border-white pl-6 break-words text-lg leading-normal lg:text-xl lg:leading-normal"
          >
            <f:if condition="{description}">
              {description -> f:format.html()}
            </f:if>
          </div>
          <svg
            class="absolute z-10 left-0 sm:left-[unset] sm:right-6 xl:right-16 -bottom-px fill-white text-white dark:text-gray-900"
            xmlns="http://www.w3.org/2000/svg"
            width="320"
            height="44"
            viewBox="0 0 320 44"
            fill="none"
          >
            <path
              d="M158.476 3.62637L0 44L320 44L217.432 6.58605C198.526 -0.310533 177.978 -1.34204 158.476 3.62637Z"
              fill="currentColor"
            />
          </svg>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
