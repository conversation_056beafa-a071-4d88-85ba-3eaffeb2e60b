<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="headline" type="string" />
    <fc:param name="layout" type="string" />
    <fc:param name="subline" type="string" optional="1" />
    <fc:param name="overline" type="string" optional="1" />
    <fc:param name="truncate" type="integer" optional="1" default="0" />

    <fc:renderer>
      <f:switch expression="{layout}">
        <f:case value="101">
          <f:variable name="headlineTypeTag" value="h1" />
          <f:variable name="headlineTypeClass" value="heading-type-1" />
        </f:case>
        <f:case value="102">
          <f:variable name="headlineTypeTag" value="h1" />
          <f:variable name="headlineTypeClass" value="heading-type-2" />
        </f:case>
        <f:case value="103">
          <f:variable name="headlineTypeTag" value="h2" />
          <f:variable name="headlineTypeClass" value="heading-type-3" />
        </f:case>
        <f:case value="104">
          <f:variable name="headlineTypeTag" value="h3" />
          <f:variable name="headlineTypeClass" value="heading-type-4" />
        </f:case>
        <f:case value="105">
          <f:variable name="headlineTypeTag" value="h4" />
          <f:variable name="headlineTypeClass" value="heading-type-5" />
        </f:case>
        <f:case value="106">
          <f:variable name="headlineTypeTag" value="h5" />
          <f:variable name="headlineTypeClass" value="heading-type-6" />
        </f:case>
        <f:case value="107">
          <f:variable name="headlineTypeTag" value="h6" />
          <f:variable name="headlineTypeClass" value="heading-type-7" />
        </f:case>
        <f:case value="hero">
          <f:variable name="headlineTypeTag" value="h1" />
          <f:variable name="headlineTypeClass" value="heading-type-hero" />
        </f:case>
        <f:defaultCase>
          <f:variable name="headlineTypeTag" value="h1" />
          <f:variable name="headlineTypeClass" value="hidden" />
        </f:defaultCase>
      </f:switch>

      <f:if condition="{truncate}">
        <f:variable name="class" value="{class} truncate" />
      </f:if>

      <gdmcom:dynamicTag
        as="{headlineTypeTag}"
        class="{headlineTypeClass} text-gray-900 group-dark/header:text-white dark:text-white {class}"
      >
        <f:format.raw>
          {v:format.pregReplace( pattern: "/break\;/", replacement: "<br />",
          subject: headline )}
        </f:format.raw>
      </gdmcom:dynamicTag>
    </fc:renderer>
  </fc:component>
</html>
