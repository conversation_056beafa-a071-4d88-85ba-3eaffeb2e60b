<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/vhs/ViewHelpers"
  xmlns:gdmcom="http://typo3.org/ns/Mogic/GdmCom/ViewHelpers"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-show="http://example.org/dummy-ns"
>
  <f:layout name="DefaultPlugin" />

  <f:section name="Form">
    <form
      x-bind="jobFilterBinding"
      class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8 mb-8"
    >
      <f:for each="{options}" key="filterKey" as="values" iteration="iterator">
        <f:switch expression="{filterKey}">
          <f:case value="company">
            <f:variable name="label" value="Unternehmen" />
            <f:variable name="icon" value="default/home.svg" />
          </f:case>
          <f:case value="location">
            <f:variable name="label" value="Standort" />
            <f:variable name="icon" value="default/map-pin.svg" />
          </f:case>
          <f:case value="carreer">
            <f:variable name="label" value="Karrierelevel" />
            <f:variable name="icon" value="default/academic-cap.svg" />
          </f:case>
        </f:switch>
        <gdmcomc:dropdown
          label="{label}"
          icon="{icon}"
          name="tx_gdmcom_joblist[{filterKey}][]"
          options="{values}"
          selected="{selected.{filterKey}}"
        />
      </f:for>
    </form>
  </f:section>

  <f:section name="Item">
    <f:variable name="itemData" value="{ itemCycle: iterator.cycle }" />
    <f:variable
      name="itemJsonData"
      value="{itemData -> v:format.json.encode()}"
    />
    <f:variable name="additionalAttributes" value="{x-data: itemJsonData}" />

    <v:variable.set name="additionalAttributes.x-bind" value="jobItemBinding" />

    <f:link.typolink
      parameter="{card.detailUrl}"
      target="{f:if(condition: card.detailNewTab, then: '_blank', else: '')}"
      title="{card.detailUrl.title}"
      additionalAttributes="{additionalAttributes}"
      class="group/jobcard md:flex rounded-lg hover:shadow-lg {card.detailUrl.class}"
    >
      <gdmcomc:content.card
        header="{
          headline: card.title,
          layout: '106'
        }"
      >
        <div class="grid grid-cols-1 lg:flex gap-2 lg:gap-8 flex-wrap">
          <p
            class="flex items-center gap-2 font-base text-gray-900 dark:text-white"
          >
            <gdmcomc:icon
              name="default/clock.svg"
              class="size-6 text-black dark:text-white"
            />
            <span>{card.modelStr}</span>
          </p>
          <p
            class="flex items-center gap-2 font-base text-gray-900 dark:text-white"
          >
            <gdmcomc:icon
              name="default/calendar.svg"
              class="size-6 text-black dark:text-white"
            />
            <span>{card.tx_gdmcom_jobstart}</span>
          </p>
          <p
            class="flex items-center gap-2 font-base text-gray-900 dark:text-white"
          >
            <gdmcomc:icon
              name="default/map-pin.svg"
              class="size-6 text-black dark:text-white"
            />
            <span>{card.locationStr}</span>
          </p>
        </div>
      </gdmcomc:content.card>
    </f:link.typolink>
  </f:section>

  <f:section name="Main">
    <f:variable name="mobileItemLimit" value="10" />
    <f:variable name="lengthOfRecords" value="{jobPageRecords -> f:count()}" />
    <f:variable
      name="listModuleData"
      value="{
        limit: mobileItemLimit,
        length: lengthOfRecords
      }"
    />

    <div
      x-data="list({listModuleData -> v:format.json.encode()})"
      class="flex flex-col gap-4"
    >
      <f:if condition="!{settings.hide_filter}">
        <f:render
          section="Form"
          arguments="{
          options: filterOptions,
          selected: selected
        }"
        />
      </f:if>

      <p><strong>{jobCount}</strong> Jobangebote</p>

      <f:for each="{jobPageRecords}" as="card" iteration="iterator">
        <f:render
          section="Item"
          arguments="{
          card: card,
          iterator: iterator
        }"
        />
      </f:for>

      <f:if condition="{lengthOfRecords} > {mobileItemLimit}">
        <button
          type="button"
          x-on:click="showMore"
          class="md:hidden w-fit px-5 py-2.5 mt-4 self-center bg-primary-500 hover:bg-primary-200 inline-flex items-center hover:underline font-bold justify-between gap-x-2 focus-visible:outline-none focus:ring-4 focus:ring-secondary-500 rounded-lg dark:bg-primary-300 dark:hover:bg-primary-500 dark:focus:bg-primary-500 dark:text-black"
          x-show="showButton"
        >
          Mehr anzeigen
          <gdmcomc:icon name="default/arrow-right" class="size-4" />
        </button>
      </f:if>
    </div>
  </f:section>
</html>
