<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
>
  <f:layout name="DefaultPlugin" />

  <f:section name="Canvas">
    <f:for each="{items}" as="item">
      <f:render
        section="Item"
        arguments="{
        item: item,
        layoutClass: itemLayoutClass
      }"
      />
    </f:for>
  </f:section>

  <f:section name="Item">
    <f:link.typolink
      parameter="{item.detailUrl}"
      target="{f:if(condition: item.detailNewTab, then: '_blank', else: '')}"
      title="{item.detailUrl.title}"
      class="{layoutClass}"
    >
      <gdmcomc:content.card
        header="{
          headline: item.title,
          layout: '106'
        }"
      >
        <div class="grid grid-cols-1 gap-2">
          <p
            class="flex items-center gap-2 font-base text-gray-900 dark:text-white"
          >
            <gdmcomc:icon
              name="default/clock.svg"
              class="size-6 text-black dark:text-white"
            />
            <span>{item.modelStr}</span>
          </p>
          <p
            class="flex items-center gap-2 font-base text-gray-900 dark:text-white"
          >
            <gdmcomc:icon
              name="default/calendar.svg"
              class="size-6 text-black dark:text-white"
            />
            <span>{item.tx_gdmcom_jobstart}</span>
          </p>
          <p
            class="flex items-center gap-2 font-base text-gray-900 dark:text-white"
          >
            <gdmcomc:icon
              name="default/map-pin.svg"
              class="size-6 text-black dark:text-white"
            />
            <span>{item.locationStr}</span>
          </p>
        </div>
      </gdmcomc:content.card>
    </f:link.typolink>
  </f:section>

  <f:section name="Main">
    <f:variable
      name="itemLayoutClass"
      value="group/jobcard flex-none w-full lg:w-1/2 xl:w-1/4 px-4"
    />
    <f:variable name="carouselModuleData" value="{}" />
    <gdmcomc:carousel module-data="{carouselModuleData}">
      <f:render
        section="Canvas"
        arguments="{
        items: jobPageRecords,
        itemLayoutClass: itemLayoutClass
      }"
      />
    </gdmcomc:carousel>
  </f:section>
</html>
