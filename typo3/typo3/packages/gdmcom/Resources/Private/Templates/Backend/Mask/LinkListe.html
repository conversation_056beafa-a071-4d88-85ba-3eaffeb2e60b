<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
    data-namespace-typo3-fluid="true"
	>
    <f:layout name="Default" />

    <f:section name="Main">
        <f:if condition="{data.tx_mask_header}">
            <h2 class="text-base text-black font-bold mb-4">{data.tx_mask_header}</h2>
        </f:if>
        <f:if condition="{data.tx_mask_links}">
            <ul class="flex flex-col gap-4">
                <f:for each="{data.tx_mask_links}" as="item">
                    <li class="text-gray-100">
                        <f:variable name="title" value="{f:split(value: '{item.tx_mask_link}', separator: '- -')}" />
                        <f:if condition="{title -> f:count()} == 2">
                            <f:then>
                                <f:link.typolink parameter="{item.tx_mask_link}">
                                    {title.1 -> f:format.trim() -> v:format.pregReplace(pattern: '["]', replacement: '')}
                                </f:link.typolink>
                            </f:then>
                            <f:else>
                                    <f:link.typolink parameter="{item.tx_mask_link}"></f:link.typolink>
                            </f:else>
                        </f:if>
                    </li>
                </f:for>
            </ul>
        </f:if>
    </f:section>
</html>
