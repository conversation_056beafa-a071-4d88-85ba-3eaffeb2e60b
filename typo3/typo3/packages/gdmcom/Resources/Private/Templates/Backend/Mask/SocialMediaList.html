<html
    xmlns:v="http://typo3.org/ns/TYPO3/CMS/Vhs/ViewHelpers"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"
    data-namespace-typo3-fluid="true"
    >
        <ul class="flex flex-wrap gap-4">
            <f:for each="{data.tx_mask_icons}" as="item">
                <li>
                    <f:variable name="icon" value="{item.tx_mask_icon}"/>
                    <f:variable name="link" value="{item.tx_mask_link}" />

                    <f:if condition="{link}">
                        <f:link.typolink parameter="{link}"></f:link.typolink>
                    </f:if>
                </li>
            </f:for>
        </ul>
</html>

