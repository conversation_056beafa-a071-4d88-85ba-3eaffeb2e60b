<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"

>
    <f:layout name="Default" />

    <f:section name="Main">
        <f:for each="{data.assets}" as="file">
            <f:media file="{file}" width="200" />
            {file.description}
        </f:for>
        <div>
            <f:if condition="{data.tx_mask_badge}">
                {data.tx_mask_badge}
            </f:if>
        </div>
        <div>
            <f:if condition="{data.header}">
                {data.header}
            </f:if>
        </div>
        <div>
            <f:if condition="{data.bodytext}">
                {data.bodytext -> f:format.raw()}
            </f:if>
        </div>
        <f:if condition="{data.tx_mask_link}">
            <f:link.typolink parameter="{data.tx_mask_link}"></f:link.typolink>
        </f:if>
    </f:section>
</html>
