<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:gdmcomc="http://typo3.org/ns/Mogic/GdmCom/Components"

>
    <f:layout name="Default" />

    <f:section name="Main">
        <f:for each="{data.assets}" as="file">
            <f:if condition="{file.type} == 4">
                <f:then>
                    <div class="aspect-[16/9] {textAligmentClass.column1}">
                        <f:media file="{file}" width="300" height="200" additionalAttributes="{controls: 'controls', class: 'h-full w-full'}" />
                    </div>
                </f:then>
                <f:else>
                    <f:image image="{file}" width="200" />
                </f:else>
            </f:if>
        </f:for>

        <h2>{data.header}</h2>

        <div style="width:200px; margin-bottom:20px; margin-top:20px">
            <f:if condition="{data.tx_mask_logo}">
                <gdmcomc:logo name="{data.tx_mask_logo}"/>
            </f:if>
        </div>

        <f:if condition="{data.bodytext}">
            {data.bodytext -> f:format.raw()}
        </f:if>

        <f:if condition="{data.tx_mask_link}">
            <f:link.typolink
                parameter="{data.tx_mask_link}"
                target="{data.tx_mask_link.target}"
                title="{data.tx_mask_link.title}"
            />
        </f:if>
    </f:section>
</html>
