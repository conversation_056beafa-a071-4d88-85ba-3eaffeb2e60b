<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
>
    <f:layout name="Default" />

    <f:section name="Main">
        <div class="flex flex-col gap-4">
            <div class="">
                <f:for each="{data.image}" as="file">
                    <f:image image="{file}" width="300px" height="auto" class="object-cover w-full h-full" alt="{file.alternative}" />
                </f:for>
            </div>
            <div class="">
                <div>
                    <f:if condition="{data.tx_mask_header}">
                        <h1 class="">{data.tx_mask_header}</h1>
                    </f:if>
                </div>
                <div class="">
                    <f:if condition="{data.tx_mask_description}">
                        {data.tx_mask_description -> f:format.raw()}
                    </f:if>
                </div>
            </div>
        </div>
    </f:section>
</html>
