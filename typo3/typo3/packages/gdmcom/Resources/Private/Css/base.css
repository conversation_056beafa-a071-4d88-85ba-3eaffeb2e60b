@tailwind base;

@layer base {
    :root {
        --header-height: 76px;
        --nav-hover-delay: 150ms;
        --nav-transition-duration: 200ms;
    }

    @screen 2xl {
        --header-height: 92px;
    }

    html, body {
        scroll-padding-top: var(--header-height);
    }

    input {
        @apply focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-secondary-500
    }

    /* Hybrid Navigation Performance Optimizations */

    /* Enable hardware acceleration for nav elements */
    nav, nav ul, nav li {
        transform: translateZ(0);
    }

    /* Optimize submenu animations - let CSS handle the heavy lifting */
    [role="menu"] {
        /* Use transform and opacity for 60fps animations */
        transition:
            opacity var(--nav-transition-duration) ease-in-out,
            transform var(--nav-transition-duration) ease-in-out,
            visibility 0ms linear var(--nav-transition-duration);

        /* Hardware acceleration */
        will-change: transform, opacity;
        transform: translateY(-8px) scale(0.95);
        opacity: 0;
        visibility: hidden;
    }

    /* When submenu is open - CSS handles the animation */
    [role="menu"][data-open="true"] {
        transition:
            opacity var(--nav-transition-duration) ease-in-out,
            transform var(--nav-transition-duration) ease-in-out,
            visibility 0ms linear 0ms;

        transform: translateY(0) scale(1);
        opacity: 1;
        visibility: visible;
    }

    /* Smooth hover transitions for menu items */
    nav a, nav button {
        transition: all 150ms ease-in-out;
        /* Contain layout changes for better performance */
        contain: layout style;
    }

    /* Optimize chevron rotation */
    .nav-chevron {
        transition: transform var(--nav-transition-duration) ease-in-out;
        transform-origin: center;
    }

    .nav-chevron[data-open="true"] {
        transform: rotate(-90deg);
    }

    /* Reduce motion for accessibility */
    @media (prefers-reduced-motion: reduce) {
        nav *, [role="menu"], .nav-chevron {
            transition-duration: 0.01ms !important;
        }
    }

    /* Mobile optimizations */
    @media (max-width: 1535px) {
        [role="menu"] {
            /* Simpler animations on mobile for better performance */
            transition: opacity 150ms ease-in-out;
            transform: none;
        }

        [role="menu"][data-open="true"] {
            transform: none;
        }
    }
}

[x-cloak] { display: none !important; }
