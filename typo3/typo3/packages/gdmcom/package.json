{"name": "gdmcom", "private": true, "scripts": {"dev": "yarn encore dev", "watch": "NODE_ENV=development yarn encore dev --watch", "prod": "yarn encore production"}, "dependencies": {"@alpinejs/collapse": "^3.14.1", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@symfony/webpack-encore": "^4.6.1", "@tailwindcss/container-queries": "^0.1.1", "@types/geojson": "^7946.0.14", "@types/leaflet": "^1.9.14", "alpinejs": "^3.14.1", "autoprefixer": "^10.4.20", "core-js": "^3.38.1", "flowbite": "^2.5.1", "flowbite-typography": "^1.0.3", "leaflet": "^1.9.4", "postcss": "^8.4.41", "postcss-inline-svg": "^6.0.0", "postcss-loader": "^7.0.0", "svg-spritemap-webpack-plugin": "^4.5.1", "tailwindcss": "^3.4.10", "webpack": "^5.94.0", "webpack-notifier": "^1.15.0"}, "browserslist": ["last 2 major versions"], "devDependencies": {"@types/alpinejs": "^3.13.10", "ts-loader": "^9.0.0", "typescript": "^5.5.4", "webpack-cli": "^5.1.4"}}