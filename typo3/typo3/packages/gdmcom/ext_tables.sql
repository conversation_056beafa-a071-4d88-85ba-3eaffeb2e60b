CREATE TABLE tt_content (
    tx_gdmcom_container_background_color varchar(24) DEFAULT 'base' NOT NULL,
    tx_gdmcom_container_spacing_top varchar(50) DEFAULT 'lg' NOT NULL,
    tx_gdmcom_container_spacing_bottom varchar(50) DEFAULT 'lg' NOT NULL,
    tx_gdmcom_container_content_centered tinyint(4) unsigned DEFAULT '0' NOT NULL,

);

CREATE TABLE pages (
    tx_gdmcom_highlight tinyint(4) unsigned DEFAULT '0' NOT NULL,
    tx_gdmcom_jobstart varchar(32) DEFAULT '' NOT NULL,
);

CREATE TABLE tx_gdmcom_services (
    uid int(10) unsigned NOT NULL AUTO_INCREMENT,
    pid int(10) unsigned NOT NULL DEFAULT 0,
    tstamp int(10) unsigned NOT NULL DEFAULT 0,
    crdate int(10) unsigned NOT NULL DEFAULT 0,
    hidden smallint(5) unsigned NOT NULL DEFAULT 0,
    deleted smallint(5) unsigned NOT NULL DEFAULT 0,

    header varchar(255) NOT NULL DEFAULT '',
    bodytext mediumtext NULL,
    icon varchar(255) NOT NULL DEFAULT '',
    link varchar(255) NOT NULL DEFAULT '',
    categories int(10) unsigned NOT NULL DEFAULT 0,

    PRIMARY KEY (uid)
);

CREATE TABLE tx_gdmcom_locations (
    uid int(10) unsigned NOT NULL AUTO_INCREMENT,
    pid int(10) unsigned NOT NULL DEFAULT 0,
    tstamp int(10) unsigned NOT NULL DEFAULT 0,
    crdate int(10) unsigned NOT NULL DEFAULT 0,
    hidden smallint(5) unsigned NOT NULL DEFAULT 0,
    deleted smallint(5) unsigned NOT NULL DEFAULT 0,

    title varchar(255) NOT NULL DEFAULT '',
    headquarter tinyint(4) unsigned DEFAULT '0' NOT NULL,
    address varchar(255) DEFAULT '' NOT NULL,
    loc_lat double(10,7) DEFAULT NULL,
    loc_lng double(10,7) DEFAULT NULL,
    email varchar(255) NOT NULL DEFAULT '',
    telephone varchar(255) NOT NULL DEFAULT '',
    website varchar(255) NOT NULL DEFAULT '',
    categories int(10) unsigned NOT NULL DEFAULT 0,

    PRIMARY KEY (uid)
);



CREATE TABLE sys_file_reference (
 poster varchar(255) DEFAULT '' NOT NULL
);


