{"name": "mogic/gdmcom-typo3", "description": "GDMcom TYPO3 website", "license": "GPL-2.0-or-later", "type": "project", "config": {"allow-plugins": {"typo3/class-alias-loader": true, "typo3/cms-composer-installers": true, "cweagans/composer-patches": true}, "platform": {"php": "8.1.1"}, "sort-packages": true}, "repositories": {"packages": {"type": "path", "url": "packages/*"}, "aus_driver_amazon_s3": {"type": "vcs", "url": "https://github.com/mogic-le/aus_driver_amazon_s3"}, "mask": {"type": "vcs", "url": "https://github.com/mogic-le/mask.git"}}, "require": {"andersundsehr/aus-driver-amazon-s3": "dev-mogic-master", "b13/container": "^3.1", "b13/doktypemapper": "^2.1", "b13/masi": "^2.0", "cweagans/composer-patches": "^1.7", "fluidtypo3/vhs": "^7.0", "helhum/typo3-console": "^8.1", "ichhabrecht/content-defender": "^3.4", "jigal/t3adminer": "^12.0", "mask/mask": "dev-json-split-multiple-paths", "mogic/gdmcom": "^1.0", "mogic/giby": "^1.0", "mogic/mogic-phpcs": "^2.1", "networkteam/sentry-client": "^5.1", "sitegeist/fluid-components": "^3.7", "ssch/typo3-encore": "^5.0", "symfony/var-dumper": "^6.4", "typo3/cms-backend": "^12.4.0", "typo3/cms-belog": "^12.4.0", "typo3/cms-beuser": "^12.4.0", "typo3/cms-core": "^12.4.0", "typo3/cms-dashboard": "^12.4.0", "typo3/cms-extbase": "^12.4.0", "typo3/cms-extensionmanager": "^12.4.0", "typo3/cms-felogin": "^12.4.0", "typo3/cms-filelist": "^12.4.0", "typo3/cms-fluid": "^12.4.0", "typo3/cms-fluid-styled-content": "^12.4.0", "typo3/cms-form": "^12.4.0", "typo3/cms-frontend": "^12.4.0", "typo3/cms-impexp": "^12.4.0", "typo3/cms-info": "^12.4.0", "typo3/cms-install": "^12.4.0", "typo3/cms-lowlevel": "^12.4", "typo3/cms-reactions": "^12.4.0", "typo3/cms-rte-ckeditor": "^12.4.0", "typo3/cms-scheduler": "^12.4.0", "typo3/cms-seo": "^12.4.0", "typo3/cms-setup": "^12.4.0", "typo3/cms-sys-note": "^12.4.0", "typo3/cms-t3editor": "^12.4.0", "typo3/cms-tstemplate": "^12.4.0", "typo3/cms-viewpage": "^12.4.0", "typo3/cms-webhooks": "^12.4.0"}, "extra": {"enable-patching": true, "patches": {"ssch/typo3-encore": {"[FEATURE] Add cachebuster to the SvgViewHelper #169": "patches/ssch-typo3-encore-classes-viewhelpers-svgviewhelper-php.patch"}}}}