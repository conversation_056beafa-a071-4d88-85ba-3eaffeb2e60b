<?php
return [
    'BE' => [
        'debug' => true,
        'installToolPassword' => '$argon2i$v=19$m=65536,t=16,p=1$LjhCc2FjdWNidXAubUI0Mg$KYT+eKRZJdCTU7WrkWxCuYpAv2D6Jh7I23UFpw1ZjiU',
        'passwordHashing' => [
            'className' => 'TYPO3\\CMS\\Core\\Crypto\\PasswordHashing\\Argon2iPasswordHash',
            'options' => [],
        ],
    ],
    'DB' => [
        'Connections' => [
            'Default' => [
                'charset' => 'utf8mb4',
                'dbname' => 'typo3gdmcom',
                'driver' => 'pdo_mysql',
                'host' => 'mariadb',
                'password' => 'gdmcom1rojoo',
                'port' => 3306,
                'tableoptions' => [
                    'charset' => 'utf8mb4',
                    'collate' => 'utf8mb4_unicode_ci',
                ],
                'user' => 'gdmcom',
            ],
        ],
    ],
    'EXTCONF' => [
        'lang' => [
            'availableLanguages' => [
                'de',
            ],
        ],
    ],
    'EXTENSIONS' => [
        'aus_driver_amazon_s3' => [
            'dnsPrefetch' => '1',
            'doNotLoadAmazonLib' => '0',
            'enablePermissionsCheck' => '0',
            'storage_13' => [
                'bucket' => 'typo3',
                'customHost' => 'http://minio:9000',
                'key' => 'typo3-dev',
                'protocol' => 'auto',
                'publicBaseUrl' => 'files.gdmcom.test:9068/typo3/',
                'secretKey' => 'Quai0Gei',
            ],
            'storage_3' => [
                'bucket' => 'gdmcom-typo3',
                'customHost' => 'https://files.staging.gdmcom.mogic-server.de',
                'key' => 'typo3-readonly',
                'protocol' => 'https://',
                'publicBaseUrl' => 'files.staging.gdmcom.mogic-server.de/gdmcom-typo3/',
                'secretKey' => 'La6eiBahnuv4do2k',
            ],
        ],
        'backend' => [
            'backendFavicon' => 'EXT:gdmcom/Resources/Public/Assets/Favicons/backend-favicon-32x32.png',
            'backendLogo' => 'EXT:gdmcom/Resources/Public/Assets/Favicons/android-chrome-192x192.png',
            'loginBackgroundImage' => '',
            'loginFootnote' => '',
            'loginHighlightColor' => '',
            'loginLogo' => 'EXT:gdmcom/Resources/Public/Assets/Favicons/android-chrome-512x512.png',
            'loginLogoAlt' => 'GDMcom',
        ],
        'extensionmanager' => [
            'automaticInstallation' => '1',
            'offlineMode' => '0',
        ],
        'mask' => [
            'backend' => 'EXT:gdmcom/Resources/Private/Templates/Backend/Mask,EXT:giby/Resources/Private/Templates/Backend/Mask',
            'backend_layouts_folder' => 'EXT:gdmcom/Configuration/Mask/BackendLayouts,EXT:giby/Configuration/Mask/BackendLayouts',
            'backendlayout_pids' => '0',
            'content' => 'EXT:gdmcom/Resources/Private/Templates/ContentElements/Mask,EXT:giby/Resources/Private/Templates/ContentElements/Mask',
            'content_elements_folder' => 'EXT:gdmcom/Configuration/Mask/ContentElements,EXT:giby/Configuration/Mask/ContentElements',
            'json' => '',
            'layouts' => 'EXT:gdmcom/Resources/Private/Layouts/Mask,EXT:giby/Resources/Private/Layouts/Mask',
            'layouts_backend' => 'EXT:gdmcom/Resources/Private/Layouts/Mask,EXT:giby/Resources/Private/Layouts/Mask',
            'loader_identifier' => 'json-split',
            'override_shared_fields' => '1',
            'partials' => 'EXT:gdmcom/Resources/Private/Layouts/Mask,EXT:giby/Resources/Private/Layouts/Mask',
            'partials_backend' => 'EXT:gdmcom/Resources/Private/Partials/Backend/Mask,EXT:giby/Resources/Private/Partials/Backend/Mask',
            'preview' => 'EXT:gdmcom/Resources/Public/Mask/,EXT:giby/Resources/Public/Mask/',
        ],
        'scheduler' => [
            'maxLifetime' => '1440',
        ],
        'sentry_client' => [
            'disableDatabaseLogging' => '0',
            'dsn' => 'https://<EMAIL>/88',
            'ignoreMessageRegex' => '',
            'logWriterComponentIgnorelist' => '',
            'release' => '',
            'reportDatabaseConnectionErrors' => '0',
            'reportUserInformation' => 'userid',
            'showEventId' => '1',
        ],
        't3adminer' => [
            'IPaccess' => '',
            'applyDevIpMask' => '0',
            'exportDirectory' => 'fileadmin',
        ],
        'vhs' => [
            'disableAssetHandling' => '0',
        ],
    ],
    'FE' => [
        'debug' => true,
        'passwordHashing' => [
            'className' => 'TYPO3\\CMS\\Core\\Crypto\\PasswordHashing\\Argon2iPasswordHash',
            'options' => [],
        ],
        'additionalAbsRefPrefixDirectories' => "build"
    ],
    'GFX' => [
        'imagefile_ext' => 'gif,jpg,jpeg,tif,tiff,bmp,pcx,tga,png,pdf,ai,svg,webp',
        'processor' => 'GraphicsMagick',
        'processor_allowTemporaryMasksAsPng' => false,
        'processor_colorspace' => 'RGB',
        'processor_effects' => false,
        'processor_enabled' => true,
        'processor_path' => '/usr/bin/',
    ],
    'LOG' => [
        'TYPO3' => [
            'CMS' => [
                'deprecations' => [
                    'writerConfiguration' => [
                        'notice' => [
                            'TYPO3\CMS\Core\Log\Writer\FileWriter' => [
                                'disabled' => false,
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
    'MAIL' => [
        'defaultMailFromAddress' => '<EMAIL>',
        'defaultMailFromName' => 'GDMcom TYPO3 CMS',
        'transport' => 'smtp',
        'transport_sendmail_command' => '/usr/sbin/sendmail -t -i',
        'transport_smtp_encrypt' => false,
        'transport_smtp_password' => '',
        'transport_smtp_server' => 'mailpit.mogic.com:1030',
        'transport_smtp_username' => '',
    ],
    'SYS' => [
        'caching' => [
            'cacheConfigurations' => [
                'hash' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                ],
                'imagesizes' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                    'options' => [
                        'compression' => true,
                    ],
                ],
                'pages' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                    'options' => [
                        'compression' => true,
                    ],
                ],
                'rootline' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                    'options' => [
                        'compression' => true,
                    ],
                ],
            ],
        ],
        'devIPmask' => '*',
        'displayErrors' => 1,
        'encryptionKey' => '98477146c56b4295e1e56612224b5c3947786938dc86ec62a3fc331cd0baf6892c81fc72d128ec32ef42cad60d29bd2d',
        'exceptionalErrors' => 12290,
        'mediafile_ext' => 'mp4,webm,ogg',
        'sitename' => 'GDMcom',
        'systemMaintainers' => [
            1, 2,
        ],
        'trustedHostsPattern' => '.*.*',
    ],
];
