<?php
/**
 * We have the following contexts:
 * - Production
 * - Production/Staging
 * - Development/Testing
 * - Development/Local
 */
$env = (string) TYPO3\CMS\Core\Core\Environment::getContext();

if ($env === 'Production'
    || $env === 'Production/Staging'
    || $env === 'Development/Testing'
) {
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['host']     = getenv('MYSQL_HOST');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['dbname']   = getenv('MYSQL_DATABASE');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['user']     = getenv('MYSQL_USER');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['password'] = getenv('MYSQL_PASSWORD');

    // Register exception handler
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['productionExceptionHandler']
        = Networkteam\SentryClient\ProductionExceptionHandler::class;
    // Forward log messages to Sentry
    $GLOBALS['TYPO3_CONF_VARS']['LOG']['writerConfiguration'] = [
        \TYPO3\CMS\Core\Log\LogLevel::ERROR => [
            \Networkteam\SentryClient\SentryLogWriter::class => [],
        ],
    ];

} else if ($env === 'Development/Local') {
    $GLOBALS['TYPO3_CONF_VARS']['BE']['debug'] = true;
    $GLOBALS['TYPO3_CONF_VARS']['FE']['debug'] = true;
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['displayErrors'] = '1';
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['devIPmask'] = '*';
}

//MinIO file storage
if ($env === 'Production') {
    //MinIO GDMcom
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_3'] = [
        'key'           => getenv('MINIO_LOCAL_GDMCOM_USER'),
        'secretKey'     => getenv('MINIO_LOCAL_GDMCOM_PASSWORD'),
        'publicBaseUrl' => getenv('MINIO_LOCAL_GDMCOM_PUBLIC_URL'),
        'bucket'        => getenv('MINIO_LOCAL_GDMCOM_BUCKET_NAME'),
        'customHost'    => getenv('MINIO_LOCAL_GDMCOM_CUSTOM_HOST'),
        'protocol'      => 'auto',
    ];
    //MinIO GIBY
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_4'] = [
        'key'           => getenv('MINIO_LOCAL_GIBY_USER'),
        'secretKey'     => getenv('MINIO_LOCAL_GIBY_PASSWORD'),
        'publicBaseUrl' => getenv('MINIO_LOCAL_GIBY_PUBLIC_URL'),
        'bucket'        => getenv('MINIO_LOCAL_GIBY_BUCKET_NAME'),
        'customHost'    => getenv('MINIO_LOCAL_GIBY_CUSTOM_HOST'),
        'protocol'      => 'auto',
    ];

} else {
    //prod: MinIO GDMcom
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_3'] = [
        'bucket'        => getenv('MINIO_PROD_GDMCOM_BUCKET_NAME'),
        'key'           => getenv('MINIO_PROD_GDMCOM_USER'),
        'secretKey'     => getenv('MINIO_PROD_GDMCOM_PASSWORD'),
        'customHost'    => getenv('MINIO_PROD_GDMCOM_CUSTOM_HOST'),
        'publicBaseUrl' => getenv('MINIO_PROD_GDMCOM_PUBLIC_URL'),
        'protocol'      => 'https://',
    ];
    //prod: MinIO GIBY
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_4'] = [
        'bucket'        => getenv('MINIO_PROD_GIBY_BUCKET_NAME'),
        'key'           => getenv('MINIO_PROD_GIBY_USER'),
        'secretKey'     => getenv('MINIO_PROD_GIBY_PASSWORD'),
        'customHost'    => getenv('MINIO_PROD_GIBY_CUSTOM_HOST'),
        'publicBaseUrl' => getenv('MINIO_PROD_GIBY_PUBLIC_URL'),
        'protocol'      => 'https://',
    ];

    //local: MinIO GDMcom
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_13'] = [
        'bucket'        => getenv('MINIO_LOCAL_GDMCOM_BUCKET_NAME'),
        'key'           => getenv('MINIO_LOCAL_GDMCOM_USER'),
        'secretKey'     => getenv('MINIO_LOCAL_GDMCOM_PASSWORD'),
        'customHost'    => getenv('MINIO_LOCAL_GDMCOM_CUSTOM_HOST'),
        'publicBaseUrl' => getenv('MINIO_LOCAL_GDMCOM_PUBLIC_URL'),
        'protocol'      => 'auto',
    ];
    //local: MinIO GIBY
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_14'] = [
        'bucket'        => getenv('MINIO_LOCAL_GIBY_BUCKET_NAME'),
        'key'           => getenv('MINIO_LOCAL_GIBY_USER'),
        'secretKey'     => getenv('MINIO_LOCAL_GIBY_PASSWORD'),
        'customHost'    => getenv('MINIO_LOCAL_GIBY_CUSTOM_HOST'),
        'publicBaseUrl' => getenv('MINIO_LOCAL_GIBY_PUBLIC_URL'),
        'protocol'      => 'auto',
    ];
}
