--- /dev/null
+++ ../Classes/ViewHelpers/SvgViewHelper.php
@@ -17,6 +17,7 @@
 use DOMXPath;
 use Ssch\Typo3Encore\Integration\FilesystemInterface;
 use Ssch\Typo3Encore\Integration\IdGeneratorInterface;
+use TYPO3\CMS\Core\Core\Environment;
 use TYPO3\CMS\Core\Resource\Exception\FolderDoesNotExistException;
 use TYPO3\CMS\Extbase\Service\ImageService;
 use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractTagBasedViewHelper;
@@ -73,6 +74,7 @@
         $this->registerArgument('width', 'string', 'Width of the image.');
         $this->registerArgument('height', 'string', 'Height of the image.');
         $this->registerArgument('absolute', 'bool', 'Force absolute URL', false, false);
+        $this->registerArgument('cacheBusterParameter', 'string', 'Add a cache buster parameter', false, '');
     }
 
     public function render(): string
@@ -144,6 +146,12 @@
                 }
             }
         } else {
+            if ($this->arguments['cacheBusterParameter']) {
+                $time = @filemtime(Environment::getPublicPath(). '/'. ltrim($imageUri, '/'));
+                if ($time) {
+                    $imageUri .= sprintf('?%s=%s', $this->arguments['cacheBusterParameter'], $time);
+                }
+            }
             $content[] = sprintf(
                 '<use xlink:href="%s#%s" />',
                 $imageUri,
