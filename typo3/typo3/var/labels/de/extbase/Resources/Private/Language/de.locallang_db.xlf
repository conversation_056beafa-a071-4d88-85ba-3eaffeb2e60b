<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:extbase/Resources/Private/Language/locallang_db.xlf" date="2011-10-17T20:22:32Z" product-name="extbase" target-language="de">
    <header/>
    <body>
      <trans-unit id="fe_users.tx_extbase_type" resname="fe_users.tx_extbase_type" approved="yes">
        <source>Record Type</source>
        <target state="final">Datensatztyp</target>
      </trans-unit>
      <trans-unit id="fe_users.tx_extbase_type.0" resname="fe_users.tx_extbase_type.0" approved="yes">
        <source>undefined</source>
        <target state="final">nicht definiert</target>
      </trans-unit>
      <trans-unit id="fe_users.tx_extbase_type.Tx_Extbase_Domain_Model_FrontendUser" resname="fe_users.tx_extbase_type.Tx_Extbase_Domain_Model_FrontendUser" approved="yes">
        <source>Tx_Extbase_Domain_Model_FrontendUser</source>
        <target state="final">Tx_Extbase_Domain_Model_FrontendUser</target>
      </trans-unit>
      <trans-unit id="fe_groups.tx_extbase_type" resname="fe_groups.tx_extbase_type" approved="yes">
        <source>Record Type</source>
        <target state="final">Datensatztyp</target>
      </trans-unit>
      <trans-unit id="fe_groups.tx_extbase_type.0" resname="fe_groups.tx_extbase_type.0" approved="yes">
        <source>undefined</source>
        <target state="final">nicht definiert</target>
      </trans-unit>
      <trans-unit id="fe_groups.tx_extbase_type.Tx_Extbase_Domain_Model_FrontendUserGroup" resname="fe_groups.tx_extbase_type.Tx_Extbase_Domain_Model_FrontendUserGroup" approved="yes">
        <source>Tx_Extbase_Domain_Model_FrontendUserGroup</source>
        <target state="final">Tx_Extbase_Domain_Model_FrontendUserGroup</target>
      </trans-unit>
    </body>
  </file>
</xliff>
