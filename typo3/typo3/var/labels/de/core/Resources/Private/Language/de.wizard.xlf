<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:core/Resources/Private/Language/wizard.xlf" date="2016-02-14T16:11:24Z" product-name="core" target-language="de">
    <header/>
    <body>
      <trans-unit id="wizard.button.next" resname="wizard.button.next" approved="yes">
        <source>Next</source>
        <target state="final">Weiter</target>
      </trans-unit>
      <trans-unit id="wizard.button.prev" approved="yes">
        <source>Previous</source>
        <target state="final">Zurück</target>
      </trans-unit>
      <trans-unit id="wizard.button.cancel" resname="wizard.button.cancel" approved="yes">
        <source>Cancel</source>
        <target state="final">Abbrechen</target>
      </trans-unit>
      <trans-unit id="wizard.progress" resname="wizard.progress" approved="yes">
        <source>Step {0} of {1}</source>
        <target state="final">Schritt {0} von {1}</target>
      </trans-unit>
      <trans-unit id="wizard.progressStep" approved="yes">
        <source>Step-</source>
        <target state="final">Schritt-</target>
      </trans-unit>
      <trans-unit id="wizard.progressStep.start" approved="yes">
        <source>Start</source>
        <target state="final">Start</target>
      </trans-unit>
      <trans-unit id="wizard.progressStep.configure" approved="yes">
        <source>Configure</source>
        <target state="final">Konfigurieren</target>
      </trans-unit>
      <trans-unit id="wizard.progressStep.finish" approved="yes">
        <source>Finish!	</source>
        <target state="final">Fertig!</target>
      </trans-unit>
      <trans-unit id="wizard.processing.title" resname="wizard.processing.title" approved="yes">
        <source>Processing...</source>
        <target state="final">Verarbeitung läuft...</target>
      </trans-unit>
    </body>
  </file>
</xliff>
