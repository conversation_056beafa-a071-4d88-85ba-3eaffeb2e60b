<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:backend/Resources/Private/Language/locallang_column_selector.xlf" date="2021-08-25T02:11:02Z" product-name="backend" target-language="de">
    <header/>
    <body>
      <trans-unit id="showColumns" resname="showColumns" approved="yes">
        <source>Show columns</source>
        <target state="final">Spalten anzeigen</target>
      </trans-unit>
      <trans-unit id="showColumnsSelection" resname="showColumnsSelection" approved="yes">
        <source>Show columns for %s</source>
        <target state="final">Spalten für %s anzeigen</target>
      </trans-unit>
      <trans-unit id="updateColumnView" resname="updateColumnView" approved="yes">
        <source>Update</source>
        <target state="final">Aktualisieren</target>
      </trans-unit>
      <trans-unit id="updateColumnView.nothingUpdated" resname="updateColumnView.nothingUpdated" approved="yes">
        <source>No columns were updated</source>
        <target state="final">Keine Spalten wurden aktualisiert</target>
      </trans-unit>
      <trans-unit id="updateColumnView.error" resname="updateColumnView.error" approved="yes">
        <source>Could not update columns</source>
        <target state="final">Spalten konnten nicht aktualisiert werden</target>
      </trans-unit>
      <trans-unit id="columnsFilter" resname="columnsFilter" approved="yes">
        <source>Filter by:</source>
        <target state="final">Filtern nach:</target>
      </trans-unit>
    </body>
  </file>
</xliff>
