<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:backend/Resources/Private/Language/locallang.xlf" date="2015-01-02T11:16:11Z" product-name="backend" target-language="de">
    <header/>
    <body>
      <trans-unit id="onlineDocumentation" resname="onlineDocumentation" approved="yes">
        <source>TYPO3 Online Documentation</source>
        <target state="final">TYPO3 Online Dokumentation</target>
      </trans-unit>
      <trans-unit id="opensInANewWindow" resname="opensInANewWindow" approved="yes">
        <source>opens in a new window</source>
        <target state="final">öffnet sich in einem neuen Fenster</target>
      </trans-unit>
      <trans-unit id="no_modules_registered" resname="no_modules_registered" approved="yes">
        <source>No modules have been registered. Please contact your system administrator.</source>
        <target state="final">E<PERSON> wurden keine Module registriert. Bitte kontaktieren Sie Ihren Systemadministrator.</target>
      </trans-unit>
      <trans-unit id="show_references" resname="show_references" approved="yes">
        <source>Show references</source>
        <target state="final">Referenzen anzeigen</target>
      </trans-unit>
      <trans-unit id="systemInformation.header" resname="systemInformation.header" approved="yes">
        <source>System Information</source>
        <target state="final">Systeminformationen</target>
      </trans-unit>
      <trans-unit id="systemmessage.intro.text" resname="systemmessage.intro.text" approved="yes">
        <source>This is a short system overview. For advanced information please head to: </source>
        <target state="final">Dies ist ein kurzer Systemüberblick. Für weitergehende Informationen konsultieren Sie bitte: </target>
      </trans-unit>
      <trans-unit id="systemmessage.intro.link" resname="systemmessage.intro.link" approved="yes">
        <source>Environment Module</source>
        <target state="final">Environment-Modul</target>
      </trans-unit>
      <trans-unit id="systemmessage.allgood" resname="systemmessage.allgood" xml:space="preserve" approved="yes">
				<source>Your system is fully operational.
Have a nice day.</source>
			<target state="final">Ihr System ist voll funktionsfähig.
Einen schönen Tag noch!</target></trans-unit>
      <trans-unit id="usermodule.su.list" resname="usermodule.su.list" approved="yes">
        <source>Recently switched to</source>
        <target state="final">Neulich gewechselt zu</target>
      </trans-unit>
      <trans-unit id="usermodule.su.tooltip" resname="usermodule.su.tooltip" approved="yes">
        <source>Switch to user %s</source>
        <target state="final">Wechseln zu Benutzer %s</target>
      </trans-unit>
      <trans-unit id="config.loginLogo" resname="config.loginLogo" approved="yes">
        <source>Logo: If set, this logo will be used instead of the TYPO3 logo above the login credential fields (e.g. fileadmin/images/login-logo.png or EXT:my_theme/Resources/Public/Images/login-logo.png or //domain.tld/login-logo.png).</source>
        <target state="final">Logo: Wenn gesetzt, wird dieses Logo anstelle des TYPO3-Logos über dem Anmeldefeld gezeigt (bspw. fileadmin/images/login-logo.png oder EXT:my_theme/Resources/Public/Images/login-logo.png oder //domain.tld/login-logo.png).</target>
      </trans-unit>
      <trans-unit id="config.loginLogoAlt" resname="config.loginLogoAlt" approved="yes">
        <source>Logo Alt-Text: If a custom logo is set, this text will be used as the alt attribute of the img tag.</source>
        <target state="final">Logo Alt-Text: Wenn ein eigenes Logo gesetzt ist, wird dieser Text als alt-Attribut des img Tags verwendet.</target>
      </trans-unit>
      <trans-unit id="config.loginHighlightColor" resname="config.loginHighlightColor" approved="yes">
        <source>Highlight Color: If set, this color will be used as highlight color in the login screen.</source>
        <target state="final">Hervorhebungsfarbe: Wenn gesetzt, wird diese Farbe als Hervorhebungsfarbe im Anmeldebildschirm benutzt.</target>
      </trans-unit>
      <trans-unit id="config.loginBackgroundImage" resname="config.loginBackgroundImage" approved="yes">
        <source>Background Image: If set, this image will be used as background image for the login screen (e.g. fileadmin/images/my-background.jpg or EXT:my_theme/Resources/Public/Images/my-background.jpg or //domain.tld/my-background.png).</source>
        <target state="final">Hintergrundbild: Wenn gesetzt, wird dieses Bild als Hintergrundbild für den Anmeldebildschirm genutzt (bspw. fileadmin/images/my-background.jpg oder EXT:my_theme/Resources/Public/Images/my-background.jpg oder //domain.tld/my-background.png).</target>
      </trans-unit>
      <trans-unit id="config.loginFootnote" resname="config.loginFootnote" approved="yes">
        <source>Footnote: If set, this text will be displayed on the login screen to provide copyright information for the background image (e.g. background image © 2017 by John Doe) or a descriptive text.</source>
        <target state="final">Fußnote: Wenn gesetzt, wird dieser Text auf dem Anmeldebildschirm angezeigt, um Copyright-Informationen oder eine Bildbeschreibung über das Hintergrundbild anzuzeigen (bspw. Hintergrundbild © 2017 von Max Mustermann).</target>
      </trans-unit>
      <trans-unit id="config.backendLogo" resname="config.backendLogo" approved="yes">
        <source>Logo: If set, this logo will be used instead of the TYPO3 logo in the TYPO3 backend in the left top corner (e.g. fileadmin/images/backend-logo.png or EXT:my_theme/Resources/Public/Images/backend-logo.png).</source>
        <target state="final">Logo: Wenn gesetzt, wird dieses Logo anstelle des TYPO3-Logos im TYPO3-Backend in der oberen linken Ecke angezeigt (bspw. fileadmin/images/backend-logo.png oder EXT:my_theme/Resources/Public/Images/backend-logo.png).</target>
      </trans-unit>
      <trans-unit id="config.backendFavicon" resname="config.backendFavicon" approved="yes">
        <source>Favicon: If set, this favicon will be used instead of the TYPO3 logo (e.g. EXT:my_theme/Resources/Public/Images/favicon.ico).</source>
        <target state="final">Favicon: Wenn gesetzt, wird dieses Favicon anstelle des TYPO3-Logos genutzt (bspw. EXT:my_theme/Resources/Public/Images/favicon.ico).</target>
      </trans-unit>
      <trans-unit id="foldertreeview.noFolders.title" resname="foldertreeview.noFolders.title" approved="yes">
        <source>No folders available</source>
        <target state="final">Keine Ordner verfügbar</target>
      </trans-unit>
      <trans-unit id="foldertreeview.noFolders.message" resname="foldertreeview.noFolders.message" approved="yes">
        <source>You do not have access to any folder. Please ask your administrator to fix access permissions for your account.</source>
        <target state="final">Sie haben keinen Zugriff auf irgendeinen Ordner. Bitte bitten Sie Ihren Administrator, die Zugriffsberechtigungen Ihres Benutzers anzupassen.</target>
      </trans-unit>
      <trans-unit id="login.header" resname="login.header" approved="yes">
        <source>Login</source>
        <target state="final">Anmeldung</target>
      </trans-unit>
      <trans-unit id="login.region.footnote" resname="login.region.footnote" approved="yes">
        <source>Footnote</source>
        <target state="final">Fußnote</target>
      </trans-unit>
      <trans-unit id="login.link" resname="login.link" approved="yes">
        <source>Login with username and password</source>
        <target state="final">Anmeldung mit Benutzernamen und Passwort</target>
      </trans-unit>
      <trans-unit id="login.username" resname="login.username" approved="yes">
        <source>Username</source>
        <target state="final">Benutzername</target>
      </trans-unit>
      <trans-unit id="login.password" resname="login.password" approved="yes">
        <source>Password</source>
        <target state="final">Passwort</target>
      </trans-unit>
      <trans-unit id="login.togglePassword" resname="login.togglePassword" approved="yes">
        <source>Toggle password visibility</source>
        <target state="final">Passwort ein-/ausblenden</target>
      </trans-unit>
      <trans-unit id="login.password_forget" resname="login.password_forget" approved="yes">
        <source>Forgot your password?</source>
        <target state="final">Passwort vergessen?</target>
      </trans-unit>
      <trans-unit id="login.error.capslock" resname="login.error.capslock" approved="yes">
        <source>Attention: Caps lock enabled!</source>
        <target state="final">Achtung: Feststelltaste aktiviert!</target>
      </trans-unit>
      <trans-unit id="login.error.capslockStatus" resname="login.error.capslockStatus" approved="yes">
        <source>Caps lock enabled</source>
        <target state="final">Feststelltaste aktiv</target>
      </trans-unit>
      <trans-unit id="login.news.header" resname="login.news.header" approved="yes">
        <source>System News</source>
        <target state="final">Systemnachrichten</target>
      </trans-unit>
      <trans-unit id="login.news.date" resname="login.news.date" approved="yes">
        <source>Date</source>
        <target state="final">Datum</target>
      </trans-unit>
      <trans-unit id="login.news.previous" resname="login.news.previous" approved="yes">
        <source>Previous</source>
        <target state="final">Vorherige</target>
      </trans-unit>
      <trans-unit id="login.news.next" resname="login.news.next" approved="yes">
        <source>Next</source>
        <target state="final">Nächste</target>
      </trans-unit>
      <trans-unit id="login.navigation.loginProvider" resname="login.navigation.loginProvider" approved="yes">
        <source>Login Provider</source>
        <target state="final">Anmeldeanbieter</target>
      </trans-unit>
      <trans-unit id="login.navigation.typo3" resname="login.navigation.typo3" approved="yes">
        <source>Further information about TYPO3</source>
        <target state="final">Weitere Informationen über TYPO3</target>
      </trans-unit>
      <trans-unit id="login.error.message" resname="login.error.message" approved="yes">
        <source>Your login attempt did not succeed</source>
        <target state="final">Ihr Anmeldeversuch war nicht erfolgreich</target>
      </trans-unit>
      <trans-unit id="login.error.description" resname="login.error.description" approved="yes">
        <source>Make sure to spell your username and password correctly, including upper/lowercase characters.</source>
        <target state="final">Bitte stellen Sie sicher, dass Ihr Benutzername und Passwort korrekt sind. Groß-/Kleinschreibung wird unterschieden.</target>
      </trans-unit>
      <trans-unit id="login.error.javascript" resname="login.error.javascript" approved="yes">
        <source>Please activate JavaScript!</source>
        <target state="final">Bitte aktivieren Sie JavaScript!</target>
      </trans-unit>
      <trans-unit id="login.error.cookies" resname="login.error.cookies" approved="yes">
        <source>Please activate Cookies!</source>
        <target state="final">Bitte aktivieren Sie Cookies!</target>
      </trans-unit>
      <trans-unit id="login.error.referrer" resname="login.error.referrer" approved="yes">
        <source>TYPO3 relies on HTTP referrers for security reasons in the backend. It seems your browser refused to send this information.</source>
        <target state="final">Das TYPO3 Backend nutzt aus Gründen der Sicherheit den HTTP Referrer. Es scheint als ob der verwendete Browser diese Information nicht mitsendet.</target>
      </trans-unit>
      <trans-unit id="login.process" resname="login.process" approved="yes">
        <source>Verifying Login Data ...</source>
        <target state="final">Anmeldedaten werden verifiziert...</target>
      </trans-unit>
      <trans-unit id="login.submit" resname="login.submit" approved="yes">
        <source>Login</source>
        <target state="final">Anmeldung</target>
      </trans-unit>
      <trans-unit id="login.copyrightLink" resname="login.copyrightLink" approved="yes">
        <source>More about TYPO3</source>
        <target state="final">Mehr über TYPO3</target>
      </trans-unit>
      <trans-unit id="login.typo3Logo" resname="login.typo3Logo" approved="yes">
        <source>TYPO3 logo</source>
        <target state="final">TYPO3-Logo</target>
      </trans-unit>
      <trans-unit id="login.donate" resname="login.donate" approved="yes">
        <source>Donate</source>
        <target state="final">Spenden</target>
      </trans-unit>
      <trans-unit id="formEngine.databaseRecordErrorInlineChildChild" resname="formEngine.databaseRecordErrorInlineChildChild" approved="yes">
        <source>The record with uid %2$s from table %1$s could not be retrieved from the database. This data inconsistency can occur if
				a base record has been deleted but the intermediate record from table %3$s with uid %4$s still points to it. To fix
				this situation, either delete the intermediate record, or recover the deleted record using the recycler module.
				</source>
        <target state="final">Der Datensatz mit Uid %2$s aus Tabelle %1$s konnte nicht aus der Datenbank abgerufen werden. Diese Inkonsistenz kann auftreten, wenn ein Basis-Datensatz gelöscht worden ist, während noch der Datensatz aus Tabelle %3$s mit Uid %4$s auf ihn verweist. Um dieses Problem zu beheben, löschen Sie entweder den verweisenden Datensatz oder stellen Sie den gelöschten Datensatz mit dem Papierkorb wieder her.</target>
      </trans-unit>
      <trans-unit id="modulemenu.label" resname="modulemenu.label" approved="yes">
        <source>Module Menu</source>
        <target state="final">Modulmenü</target>
      </trans-unit>
      <trans-unit id="module.noAccess.title" resname="module.noAccess.title" approved="yes">
        <source>No module access</source>
        <target state="final">Kein Zugriff auf das Modul</target>
      </trans-unit>
      <trans-unit id="module.noAccess.message" resname="module.noAccess.message" approved="yes">
        <source>You've been redirected to the "%s" module, because you currently can't access the "%s" module. You either don't have the necessary permissions or access is temporarily not possible, e.g. due to workspace restrictions.</source>
        <target state="final">Sie wurden zum Modul "%s" weitergeleitet, da Sie derzeit nicht auf das "%s" Modul zugreifen können. Entweder haben Sie nicht die erforderlichen Berechtigungen oder der Zugriff ist vorübergehend nicht möglich, z.B. aufgrund von Arbeitsbereichsbeschränkungen.</target>
      </trans-unit>
      <trans-unit id="clearcache.title" resname="clearcache.title" approved="yes">
        <source>Page cache</source>
        <target state="final">Seitencache</target>
      </trans-unit>
      <trans-unit id="clearcache.message.success" resname="clearcache.message.success" approved="yes">
        <source>Successfully cleared page cache</source>
        <target state="final">Seitencache erfolgreich geleert</target>
      </trans-unit>
      <trans-unit id="clearcache.message.error" resname="clearcache.message.error" approved="yes">
        <source>Page cache could not be cleared</source>
        <target state="final">Seitencache konnte nicht geleert werden</target>
      </trans-unit>
      <trans-unit id="error.linkHandlerTitleMissing" resname="error.linkHandlerTitleMissing" approved="yes">
        <source>[title missing]</source>
        <target state="final">[Titel fehlt]</target>
      </trans-unit>
      <trans-unit id="create_folder.title" resname="create_folder.title" approved="yes">
        <source>Create new folder</source>
        <target state="final">Neuen Ordner erstellen</target>
      </trans-unit>
      <trans-unit id="create_folder.placeholder" resname="create_folder.placeholder" approved="yes">
        <source>Your new folder name</source>
        <target state="final">Neuer Ordnername</target>
      </trans-unit>
      <trans-unit id="create_folder.submit" resname="create_folder.submit" approved="yes">
        <source>Create folder</source>
        <target state="final">Ordner erstellen</target>
      </trans-unit>
      <trans-unit id="row.deletePlaceholder.title" resname="row.deletePlaceholder.title" approved="yes">
        <source>The live pendant of this record will be deleted when this workspace record is published.</source>
        <target state="final">Die Live-Version dieses Datensatzes wird gelöscht, sobald dieser Arbeitsbereichs-Datensatz veröffentlicht wird.</target>
      </trans-unit>
      <trans-unit id="shortcut.title" resname="shortcut.title" approved="yes">
        <source>%s%s on page "%s" [%d]</source>
        <target state="final">%s%s auf Seite "%s" [%d]</target>
      </trans-unit>
      <trans-unit id="liveSearch.databaseRecordProvider.filterLabel" resname="liveSearch.databaseRecordProvider.filterLabel" approved="yes">
        <source>Database records</source>
        <target state="final">Datenbankeinträge</target>
      </trans-unit>
      <trans-unit id="liveSearch.databaseRecordProvider.typeLabel" resname="liveSearch.databaseRecordProvider.typeLabel" approved="yes">
        <source>Database record</source>
        <target state="final">Datenbankeintrag</target>
      </trans-unit>
      <trans-unit id="liveSearch.pageRecordProvider.filterLabel" resname="liveSearch.pageRecordProvider.filterLabel" approved="yes">
        <source>Pages</source>
        <target state="final">Seiten</target>
      </trans-unit>
      <trans-unit id="liveSearch.pageRecordProvider.typeLabel" resname="liveSearch.pageRecordProvider.typeLabel" approved="yes">
        <source>Page</source>
        <target state="final">Seite</target>
      </trans-unit>
      <trans-unit id="moduleMenu.dropdown.label" resname="moduleMenu.dropdown.label" approved="yes">
        <source>Module action</source>
        <target state="final">Modul-Aktion</target>
      </trans-unit>
      <trans-unit id="pagelayout.moduleMenu.dropdown.label" resname="pagelayout.moduleMenu.dropdown.label" approved="yes">
        <source>Display mode</source>
        <target state="final">Anzeigemodus</target>
      </trans-unit>
      <trans-unit id="editdocument.moduleMenu.dropdown.label" resname="editdocument.moduleMenu.dropdown.label" approved="yes">
        <source>Record language</source>
        <target state="final">Sprache des Datensatzes</target>
      </trans-unit>
    </body>
  </file>
</xliff>
