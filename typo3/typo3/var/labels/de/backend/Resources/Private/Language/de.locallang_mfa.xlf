<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:backend/Resources/Private/Language/locallang_mfa.xlf" date="2021-01-26T11:58:45Z" product-name="cms" target-language="de">
    <header/>
    <body>
      <trans-unit id="overview.title" resname="overview.title" approved="yes">
        <source>Multi-factor Authentication Overview</source>
        <target state="final">Multi-Faktor-Authentifizierung Übersicht</target>
      </trans-unit>
      <trans-unit id="overview.badge.active" resname="overview.badge.active" approved="yes">
        <source>Active</source>
        <target state="final">Aktiv</target>
      </trans-unit>
      <trans-unit id="overview.badge.locked" resname="overview.badge.locked" approved="yes">
        <source>Locked</source>
        <target state="final">Gesperrt</target>
      </trans-unit>
      <trans-unit id="overview.defaultProvider" resname="overview.defaultProvider" approved="yes">
        <source>Default provider</source>
        <target state="final">Standard-Provider</target>
      </trans-unit>
      <trans-unit id="overview.unlockLinkTitle" resname="overview.unlockLinkTitle" approved="yes">
        <source>Unlock %s</source>
        <target state="final">Entsperre %s</target>
      </trans-unit>
      <trans-unit id="overview.unlockLinkLabel" resname="overview.unlockLinkLabel" approved="yes">
        <source>Unlock</source>
        <target state="final">Entsperren</target>
      </trans-unit>
      <trans-unit id="overview.editLinkTitle" resname="overview.editLinkTitle" approved="yes">
        <source>Edit %s</source>
        <target state="final">%s bearbeiten</target>
      </trans-unit>
      <trans-unit id="overview.editLinkLabel" resname="overview.editLinkLabel" approved="yes">
        <source>Edit / Change</source>
        <target state="final">Bearbeiten / Ändern</target>
      </trans-unit>
      <trans-unit id="overview.deactivateLinkTitle" resname="overview.deactivateLinkTitle" approved="yes">
        <source>Deactivate %s</source>
        <target state="final">%s deaktivieren</target>
      </trans-unit>
      <trans-unit id="overview.deactivateLinkLabel" resname="overview.deactivateLinkLabel" approved="yes">
        <source>Deactivate</source>
        <target state="final">Deaktivieren</target>
      </trans-unit>
      <trans-unit id="overview.setupLinkTitle" resname="overview.setupLinkTitle" approved="yes">
        <source>Setup %s</source>
        <target state="final">%s einrichten</target>
      </trans-unit>
      <trans-unit id="overview.setupLinkLabel" resname="overview.setupLinkLabel" approved="yes">
        <source>Setup</source>
        <target state="final">Einrichten</target>
      </trans-unit>
      <trans-unit id="overview.setupRequired.title" resname="overview.setupRequired.title" approved="yes">
        <source>Multi-factor authentication required</source>
        <target state="final">Multi-Faktor-Authentifizierung erforderlich</target>
      </trans-unit>
      <trans-unit id="overview.setupRequired.message" resname="overview.setupRequired.message" approved="yes">
        <source>
					Your installation requires MFA. Therefore please setup one of the following providers to be
					able to continue to use the backend.
				</source>
        <target state="final">
Ihre Installation benötigt Multi-Faktor-Authentifizierung. Richten Sie daher bitte einen der folgenden Provider ein, um mit der Verwendung des Backends fortfahren zu können.</target>
      </trans-unit>
      <trans-unit id="overview.noProviders.title" resname="overview.noProviders.title" approved="yes">
        <source>No multi-factor authentication providers</source>
        <target state="final">Keine Multi-Faktor-Authentifizierungs-Provider</target>
      </trans-unit>
      <trans-unit id="overview.noProviders.message" resname="overview.noProviders.message" approved="yes">
        <source>There are currently no multi-factor authentication providers available.</source>
        <target state="final">Momentan sind keine Multi-Faktor-Authentifizierungs-Provider verfügbar.</target>
      </trans-unit>
      <trans-unit id="overview.noProviders.errorMessage" resname="overview.noProviders.errorMessage" approved="yes">
        <source>
					You are required to setup MFA but there are no providers to activate available. You should contact
					your administrator to solve this.
				</source>
        <target state="final">
Sie müssen die Multi-Faktor-Authentifizierung einrichten, es sind jedoch keine aktivierungsfähigen Provider verfügbar. Bitte kontaktieren Sie Ihren Administrator für eine Lösung dieses Problems.</target>
      </trans-unit>
      <trans-unit id="providerNotFound" resname="providerNotFound" approved="yes">
        <source>Selected MFA provider was not found!</source>
        <target state="final">Der ausgewählte Multi-Faktor-Authentifizierungs-Provider wurde nicht gefunden!</target>
      </trans-unit>
      <trans-unit id="providerNotActive" resname="providerNotActive" approved="yes">
        <source>Selected MFA provider has to be active to perform this action!</source>
        <target state="final">Der ausgewählte Multi-Faktor-Authentifizierungs-Provider muss aktiv sein, um diese Aktion durchführen zu können!</target>
      </trans-unit>
      <trans-unit id="providerActive" resname="providerActive" approved="yes">
        <source>Selected MFA provider has to be inactive to perform this action!</source>
        <target state="final">Der ausgewählte Multi-Faktor-Authentifizierungs-Provider muss inaktiv sein, um diese Aktion ausführen zu können!</target>
      </trans-unit>
      <trans-unit id="providerIsLocked" resname="providerNotFound" approved="yes">
        <source>Selected MFA provider has to be unlocked to perform this action!</source>
        <target state="final">Der ausgewählte Multi-Faktor-Authentifizierungs-Provider muss entsperrt sein, um diese Aktion ausführen zu können!</target>
      </trans-unit>
      <trans-unit id="setup.instructions" resname="setup.instructions" approved="yes">
        <source>Setup instructions</source>
        <target state="final">Einrichtungsanleitung</target>
      </trans-unit>
      <trans-unit id="setup.instructions.close" resname="setup.instructions.close" approved="yes">
        <source>Close setup instructions</source>
        <target state="final">Einrichtungs-Anweisungen schließen</target>
      </trans-unit>
      <trans-unit id="setup.title" resname="setup.title" approved="yes">
        <source>Set up %s</source>
        <target state="final">%s einrichten</target>
      </trans-unit>
      <trans-unit id="edit.title" resname="edit.title" approved="yes">
        <source>Edit %s</source>
        <target state="final">%s bearbeiten</target>
      </trans-unit>
      <trans-unit id="edit.defaultProvider" resname="edit.defaultProvider" approved="yes">
        <source>Default provider</source>
        <target state="final">Standard-Provider</target>
      </trans-unit>
      <trans-unit id="edit.defaultProvider.description" resname="edit.defaultProvider.description" approved="yes">
        <source>Select this as your default provider for login into the TYPO3 backend.</source>
        <target state="final">Wählen Sie diesen Provider als Ihren Standard-Provider für den Login in das TYPO3 Backend aus.</target>
      </trans-unit>
      <trans-unit id="edit.defaultProvider.inputLabel" resname="edit.defaultProvider.inputLabel" approved="yes">
        <source>Use as default provider</source>
        <target state="final">Als Standard-Provider verwenden</target>
      </trans-unit>
      <trans-unit id="edit.deactivateProvider" resname="edit.deactivateProvider" approved="yes">
        <source>Deactivate provider</source>
        <target state="final">Provider deaktivieren</target>
      </trans-unit>
      <trans-unit id="edit.deactivateProvider.description" resname="edit.deactivateProvider.description" approved="yes">
        <source>If you don't want to use this provider anymore, you can deactivate it here.</source>
        <target state="final">Falls Sie diesen Provider nicht länger verwenden möchten, können Sie ihn hier deaktivieren.</target>
      </trans-unit>
      <trans-unit id="edit.deactivateProvider.linkTitle" resname="edit.deactivateProvider.linkTitle" approved="yes">
        <source>Deactivate %s</source>
        <target state="final">%s deaktivieren</target>
      </trans-unit>
      <trans-unit id="edit.deactivateProvider.linkText" resname="edit.deactivateProvider.linkText" approved="yes">
        <source>Deactivate this provider</source>
        <target state="final">Diesen Provider deaktivieren</target>
      </trans-unit>
      <trans-unit id="activate.failure" resname="activate.failure" approved="yes">
        <source>Could not activate MFA provider %s. Please try again.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s konnte nicht aktiviert werden. Bitte versuchen Sie es erneut.</target>
      </trans-unit>
      <trans-unit id="activate.success" resname="activate.success" approved="yes">
        <source>Successfully activated MFA provider %s.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s erfolgreich aktiviert.</target>
      </trans-unit>
      <trans-unit id="deactivate.failure" resname="deactivate.failure" approved="yes">
        <source>Could not deactivate MFA provider %s. Please try again.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s konnte nicht deaktiviert werden. Bitte versuchen Sie es erneut.</target>
      </trans-unit>
      <trans-unit id="deactivate.success" resname="deactivate.success" approved="yes">
        <source>Successfully deactivated MFA provider %s.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s erfolgreich deaktiviert.</target>
      </trans-unit>
      <trans-unit id="unlock.failure" resname="unlock.failure" approved="yes">
        <source>Could not unlock MFA provider %s. Please try again.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s konnte nicht entsperrt werden. Bitte versuchen Sie es erneut.</target>
      </trans-unit>
      <trans-unit id="unlock.success" resname="unlock.success" approved="yes">
        <source>Successfully unlocked MFA provider %s.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s wurde erfolgreich entsperrt.</target>
      </trans-unit>
      <trans-unit id="save.failure" resname="save.failure" approved="yes">
        <source>Could not update MFA provider %s. Please try again.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s konnte nicht aktualisiert werden. Bitte versuchen Sie es erneut.</target>
      </trans-unit>
      <trans-unit id="save.success" resname="save.success" approved="yes">
        <source>Successfully updated MFA provider %s.</source>
        <target state="final">Multi-Faktor-Authentifizierungs-Provider %s erfolgreich aktualisiert.</target>
      </trans-unit>
      <trans-unit id="auth.authError" resname="auth.authError" approved="yes">
        <source>Authentication was not successful.</source>
        <target state="final">Authentifizierung war nicht erfolgreich.</target>
      </trans-unit>
      <trans-unit id="auth.submit" resname="auth.submit" approved="yes">
        <source>Verify</source>
        <target state="final">Verifizieren</target>
      </trans-unit>
      <trans-unit id="auth.cancel" resname="auth.cancel" approved="yes">
        <source>Go back</source>
        <target state="final">Zurück</target>
      </trans-unit>
      <trans-unit id="auth.locked" resname="auth.locked" approved="yes">
        <source>This provider is temporarily locked!</source>
        <target state="final">Dieser Provider ist vorübergehend gesperrt!</target>
      </trans-unit>
      <trans-unit id="auth.alternativeProviders" resname="auth.alternativeProviders" approved="yes">
        <source>Alternative providers</source>
        <target state="final">Alternative Provider</target>
      </trans-unit>
      <trans-unit id="auth.alternativeProviders.use" resname="auth.alternativeProviders.use" approved="yes">
        <source>Use %s</source>
        <target state="final">Verwende %s</target>
      </trans-unit>
      <trans-unit id="standalone.setupProvider" resname="standalone.setupProvider" approved="yes">
        <source>Set up %s</source>
        <target state="final">%s einrichten</target>
      </trans-unit>
      <trans-unit id="standalone.selection.title" resname="standalone.selection.title" approved="yes">
        <source>Set up MFA</source>
        <target state="final">MFA einrichten</target>
      </trans-unit>
      <trans-unit id="standalone.selection.message" resname="standalone.selection.message" approved="yes">
        <source>
					%s requires you setting up MFA to secure your account. Please select one of the
					available providers below.
				</source>
        <target state="final">
					%s erfordert, dass Sie MFA einrichten, um Ihr Konto zu sichern. Bitte wählen Sie einen der
					verfügbaren Anbieter unten.
				</target>
      </trans-unit>
      <trans-unit id="standalone.selection.noAvailableProviders" resname="standalone.selection.noAvailableProviders" approved="yes">
        <source>
					You are required to set up MFA. However, there are no MFA providers available for you to use.
					This might be a misconfiguration. Please get in touch with your administrator to find a solution.
				</source>
        <target state="final">
					Sie müssen MFA einrichten, aber es stehen Ihnen keine MFA-Anbieter zur Verfügung.
					Dies könnte eine Fehlkonfiguration sein. Bitte kontaktieren Sie Ihren Administrator, um eine Lösung zu finden.
				</target>
      </trans-unit>
      <trans-unit id="standalone.selection.goBack" resname="standalone.selection.goBack" approved="yes">
        <source>Go back</source>
        <target state="final">Zurück</target>
      </trans-unit>
      <trans-unit id="standalone.setup" resname="standalone.setup" approved="yes">
        <source>Setup</source>
        <target state="final">Einrichten</target>
      </trans-unit>
      <trans-unit id="standalone.setup.cancel" resname="setup.standalone.cancel" approved="yes">
        <source>Cancel</source>
        <target state="final">Abbrechen</target>
      </trans-unit>
      <trans-unit id="standalone.setup.success.title" resname="activate.success" approved="yes">
        <source>MFA setup successful</source>
        <target state="final">MFA-Einrichtung erfolgreich</target>
      </trans-unit>
      <trans-unit id="standalone.setup.success.message" resname="activate.success" approved="yes">
        <source>
					You have successfully activated MFA provider %s. This provider was also automatically set as your
					default provider. You can change this at any time in your user settings.
				</source>
        <target state="final">
					Sie haben erfolgreich den MFA-Anbieter %s aktiviert. Dieser Provider wurde auch automatisch als Ihr
					Standardprovider festgelegt. Sie können dies jederzeit in Ihren Benutzereinstellungen ändern.
				</target>
      </trans-unit>
      <trans-unit id="ajax.success" approved="yes">
        <source>Success</source>
        <target state="final">Erfolgreich</target>
      </trans-unit>
      <trans-unit id="ajax.error" approved="yes">
        <source>An error occurred</source>
        <target state="final">Es ist ein Fehler aufgetreten</target>
      </trans-unit>
      <trans-unit id="ajax.invalidRequest" approved="yes">
        <source>Invalid request could not be processed</source>
        <target state="final">Ungültige Anfrage konnte nicht verarbeitet werden</target>
      </trans-unit>
      <trans-unit id="ajax.insufficientPermissions" approved="yes">
        <source>Your are not allowed to perform this action</source>
        <target state="final">Sie sind nicht berechtigt, diese Aktion durchzuführen</target>
      </trans-unit>
      <trans-unit id="ajax.deactivate.providersNotDeactivated" approved="yes">
        <source>No provider has been deactivated</source>
        <target state="final">Kein Provider wurde deaktiviert</target>
      </trans-unit>
      <trans-unit id="ajax.deactivate.providersDeactivated" approved="yes">
        <source>Successfully deactivated all active providers for user %s</source>
        <target state="final">Alle aktiven Provider für Benutzer %s erfolgreich deaktiviert</target>
      </trans-unit>
      <trans-unit id="ajax.deactivate.providerNotFound" approved="yes">
        <source>Provider %s could not be found</source>
        <target state="final">Provider %s konnte nicht gefunden werden</target>
      </trans-unit>
      <trans-unit id="ajax.deactivate.providerNotDeactivated" approved="yes">
        <source>Could not deactivate provider %s</source>
        <target state="final">Provider %s konnte nicht deaktiviert werden</target>
      </trans-unit>
      <trans-unit id="ajax.deactivate.providerDeactivated" approved="yes">
        <source>Successfully deactivated provider %s for user %s</source>
        <target state="final">Provider %s für Benutzer %s erfolgreich deaktiviert</target>
      </trans-unit>
    </body>
  </file>
</xliff>
