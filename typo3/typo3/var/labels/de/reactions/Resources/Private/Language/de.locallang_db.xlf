<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:reactions/Resources/Private/Language/locallang_db.xlf" date="2022-08-15T20:22:32Z" product-name="reactions" target-language="de">
    <header/>
    <body>
      <trans-unit id="reactions" resname="reactions" approved="yes">
        <source>Reactions</source>
        <target state="final">Reactions</target>
      </trans-unit>
      <trans-unit id="sys_reaction" resname="sys_reaction" approved="yes">
        <source>System Reactions</source>
        <target state="final">System Reactions</target>
      </trans-unit>
      <trans-unit id="sys_reaction.name" resname="sys_reaction.name" approved="yes">
        <source>Name</source>
        <target state="final">Name</target>
      </trans-unit>
      <trans-unit id="sys_reaction.name.description" resname="sys_reaction.name.description" approved="yes">
        <source>Meaningful name of the reaction</source>
        <target state="final">Aussagekräftiger Name</target>
      </trans-unit>
      <trans-unit id="sys_reaction.description" resname="sys_reactions.description" approved="yes">
        <source>Description</source>
        <target state="final">Beschreibung</target>
      </trans-unit>
      <trans-unit id="sys_reaction.description.description" resname="sys_reactions.description.description" approved="yes">
        <source>Additional information about the reaction.</source>
        <target state="final">Zusätzliche Informationen.</target>
      </trans-unit>
      <trans-unit id="sys_reaction.identifier" resname="sys_reaction.identifier" approved="yes">
        <source>Identifier</source>
        <target state="final">Eindeutige Kennung</target>
      </trans-unit>
      <trans-unit id="sys_reaction.identifier.description" resname="sys_reaction.identifier.description" approved="yes">
        <source>This is your unique reaction identifier within the TYPO3 URL</source>
        <target state="final">Das ist die eindeutige Kennung dieser Reaction in der TYPO3 URL</target>
      </trans-unit>
      <trans-unit id="sys_reaction.secret" resname="sys_reaction.secret" approved="yes">
        <source>Secret</source>
        <target state="final">Geheimer Schlüssel</target>
      </trans-unit>
      <trans-unit id="sys_reaction.secret.description" resname="sys_reaction.secret.description" approved="yes">
        <source>The secret is required to call the reaction from the outside. The secret can be re-created anytime, but will only be visible once (until the record got saved).</source>
        <target state="final">Der geheime Schlüssel ist erforderlich, um die Reaction von außerhalb aufzurufen. Der geheime Schlüssel kann jederzeit neu erstellt werden, ist aber nur einmal sichtbar (bis der Datensatz gespeichert wurde).</target>
      </trans-unit>
      <trans-unit id="sys_reaction.secret.passwordGenerator" resname="sys_reaction.secret.passwordGenerator" approved="yes">
        <source>Generate secret token</source>
        <target state="final">Geheimen Schlüssel erzeugen</target>
      </trans-unit>
      <trans-unit id="sys_reaction.reaction_type" resname="sys_reaction.reaction_type" approved="yes">
        <source>Reaction Type</source>
        <target state="final">Typ</target>
      </trans-unit>
      <trans-unit id="sys_reactions.reaction_type.description" resname="sys_reactions.reaction_type.description" approved="yes">
        <source>Select the reaction type to be configured.</source>
        <target state="final">Wählen Sie den zu konfigurierenden Typ der Reaction.</target>
      </trans-unit>
      <trans-unit id="sys_reaction.reaction_type.select" resname="sys_reaction.reaction_type.select" approved="yes">
        <source>Select a reaction</source>
        <target state="final">Typ auswählen</target>
      </trans-unit>
      <trans-unit id="sys_reaction.reaction_type.create_record" resname="sys_reaction.reaction_type.create_record" approved="yes">
        <source>Create database record</source>
        <target state="final">Datensatz erstellen</target>
      </trans-unit>
      <trans-unit id="sys_reaction.impersonate_user" resname="sys_reaction.impersonate_user" approved="yes">
        <source>Impersonate User</source>
        <target state="final">Benutzer imitieren</target>
      </trans-unit>
      <trans-unit id="sys_reaction.impersonate_user.description" resname="sys_reaction.impersonate_user.description" approved="yes">
        <source>Select the user with the appropriate access rights that is allowed to add a record of this type. If in doubt, use the CLI user.</source>
        <target state="final">Wählen Sie den Benutzer mit entsprechenden Zugriffsrechten, der einen Datensatz dieses Typs erstellen darf. Im Zweifelsfall verwenden Sie den CLI-Benutzer.</target>
      </trans-unit>
      <trans-unit id="sys_reaction.table_name" resname="sys_reaction.table_name" approved="yes">
        <source>Table</source>
        <target state="final">Tabelle</target>
      </trans-unit>
      <trans-unit id="sys_reaction.table_name.description" resname="sys_reaction.table_name.description" approved="yes">
        <source>Select one tables to display the corresponding fields.</source>
        <target state="final">Wählen Sie eine Tabelle aus, um die entsprechenden Felder anzuzeigen.</target>
      </trans-unit>
      <trans-unit id="sys_reaction.table_name.select" resname="sys_reaction.table_name.select" approved="yes">
        <source>Select a table</source>
        <target state="final">Tabelle auswählen</target>
      </trans-unit>
      <trans-unit id="sys_reaction.storage_pid" resname="sys_reaction.storage_pid" approved="yes">
        <source>Storage PID</source>
        <target state="final">Ordner/Seite zur Speicherung der Einträge</target>
      </trans-unit>
      <trans-unit id="sys_reaction.storage_pid.description" resname="sys_reaction.storage_pid.description" approved="yes">
        <source>Select the page on which a new record is created on.</source>
        <target state="final">Wähle die Seite, auf der ein neuer Datensatz erstellt wird.</target>
      </trans-unit>
      <trans-unit id="sys_reaction.fields" resname="sys_reaction.fields" approved="yes">
        <source>Fields</source>
        <target state="final">Felder</target>
      </trans-unit>
      <trans-unit id="sys_reaction.fields.description" resname="sys_reaction.fields.description" approved="yes">
        <source>The available fields depend on the selected table. The usage of placeholders like ${foo.bar} is possible.</source>
        <target state="final">Die verfügbaren Felder hängen von der ausgewählten Tabelle ab. Die Verwendung von Platzhaltern wie ${foo.bar} ist möglich.</target>
      </trans-unit>
      <trans-unit id="palette.config" resname="palette.config" approved="yes">
        <source>Configuration</source>
        <target state="final">Konfiguration</target>
      </trans-unit>
      <trans-unit id="palette.config.description" resname="palette.config.description" approved="yes">
        <source>General configuration of the reaction.</source>
        <target state="final">Allgemeine Konfiguration dieser Reaction.</target>
      </trans-unit>
      <trans-unit id="palette.additional" resname="palette.additional" approved="yes">
        <source>Additional configuration</source>
        <target state="final">Zusätzliche Konfiguration</target>
      </trans-unit>
      <trans-unit id="palette.additional.description" resname="palette.additional.description" approved="yes">
        <source>Specific configuration for the selected reaction type</source>
        <target state="final">Spezifische Konfiguration für den ausgewählten Reaktionstyp</target>
      </trans-unit>
      <trans-unit id="fieldMapElement.noFields" resname="fieldMapElement.noFields" approved="yes">
        <source>The selected table '%s' does not define any valid fields to be configured.</source>
        <target state="final">Die ausgewählte Tabelle '%s' enthält keine gültigen Felder, die konfiguriert werden können.</target>
      </trans-unit>
    </body>
  </file>
</xliff>
