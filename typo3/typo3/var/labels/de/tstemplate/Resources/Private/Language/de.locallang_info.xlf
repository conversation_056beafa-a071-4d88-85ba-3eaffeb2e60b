<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:tstemplate/Resources/Private/Language/locallang_info.xlf" date="2011-10-17T20:22:37Z" product-name="tstemplate" target-language="de">
    <header/>
    <body>
      <trans-unit id="submodule.title" resname="submodule.title" approved="yes">
        <source>Edit TypoScript record</source>
        <target state="final">TypoScript Datensatz bearbeiten</target>
      </trans-unit>
      <trans-unit id="submodule.titleWithRecord" resname="submodule.titleWithRecord" approved="yes">
        <source>Edit TypoScript record "%s"</source>
        <target state="final">TypoScript Datensatz "%s " bearbeiten</target>
      </trans-unit>
      <trans-unit id="submodule.description" resname="submodule.description" approved="yes">
        <source>Modify the content of the selected TypoScript record of the current page. Modifications can be made either for configurations like TypoScript constants and setup or basics like the title and description of the TypoScript record itself.</source>
        <target state="final">Ändert den Inhalt des ausgewählten TypoScript Datensatzes der aktuellen Seite. Änderungen können entweder an Konfigurationen, wie TypoScript-Konstanten und -Setup, oder grundlegenden Optionen wie dem Titel und der Beschreibung des TypoScript Datensatzes selbst vorgenommen werden.</target>
      </trans-unit>
      <trans-unit id="options.selectedRecord" resname="options.selectedRecord" approved="yes">
        <source>Selected record</source>
        <target state="final">Ausgewählter Datensatz</target>
      </trans-unit>
      <trans-unit id="table.column.constants" resname="table.column.constants" approved="yes">
        <source>Constants</source>
        <target state="final">Konstanten</target>
      </trans-unit>
      <trans-unit id="table.column.description" resname="table.column.description" approved="yes">
        <source>Description</source>
        <target state="final">Beschreibung</target>
      </trans-unit>
      <trans-unit id="table.column.lines" resname="table.column.lines" approved="yes">
        <source>%s lines</source>
        <target state="final">%s Zeilen</target>
      </trans-unit>
      <trans-unit id="table.column.setup" resname="table.column.setup" approved="yes">
        <source>Setup</source>
        <target state="final">Setup</target>
      </trans-unit>
      <trans-unit id="table.column.title" resname="table.column.title" approved="yes">
        <source>Title</source>
        <target state="final">Titel</target>
      </trans-unit>
      <trans-unit id="btn.editTypoScriptRecord" resname="btn.editTypoScriptRecord" approved="yes">
        <source>Edit the whole TypoScript record</source>
        <target state="final">Den gesamten TypoScript Datensatz bearbeiten</target>
      </trans-unit>
    </body>
  </file>
</xliff>
