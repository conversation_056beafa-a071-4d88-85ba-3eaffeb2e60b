<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:tstemplate/Resources/Private/Language/locallang.xlf" date="2011-10-17T20:22:37Z" product-name="tstemplate" target-language="de">
    <header/>
    <body>
      <trans-unit id="module.typoscript.title" resname="module.typoscript.title" approved="yes">
        <source>TypoScript</source>
        <target state="final">TypoScript</target>
      </trans-unit>
      <trans-unit id="module.typoscript.shortDescription" resname="module.typoscript.shortDescription" approved="yes">
        <source>TypoScript tools</source>
        <target state="final">TypoScript Werkzeuge</target>
      </trans-unit>
      <trans-unit id="module.typoscript.description" resname="module.typoscript.description" approved="yes">
        <source>Here you manage the TypoScript records which are in charge of the look of your website on the frontend. The module provides specialized features like a TypoScript tree, a constant editor and raw editing facilities.</source>
        <target state="final">Hier verwaltest du die TypoScript-Datensätze, die für das Aussehen deiner Website im Frontend verantwortlich sind. Das Modul bietet spezielle Funktionen wie einen TypoScript-Baum, einen Konstanten-Editor und direkte Bearbeitungsmöglichkeiten.</target>
      </trans-unit>
      <trans-unit id="submodules.option.constantEditor" resname="submodules.option.constantEditor" approved="yes">
        <source>Constant Editor</source>
        <target state="final">Konstanten-Editor</target>
      </trans-unit>
      <trans-unit id="submodules.option.infoModify" resname="submodules.option.infoModify" approved="yes">
        <source>Edit TypoScript Record</source>
        <target state="final">TypoScript Datensatz bearbeiten</target>
      </trans-unit>
      <trans-unit id="submodules.option.active" resname="submodules.option.active" approved="yes">
        <source>Active TypoScript</source>
        <target state="final">Aktives TypoScript</target>
      </trans-unit>
      <trans-unit id="submodules.option.templateAnalyzer" resname="submodules.option.templateAnalyzer" approved="yes">
        <source>Included TypoScript</source>
        <target state="final">Eingebundenes TypoScript</target>
      </trans-unit>
      <trans-unit id="submodules.option.templateRecordsOverview" resname="submodules.option.templateRecordsOverview" approved="yes">
        <source>TypoScript Records Overview</source>
        <target state="final">TypoScript Datensatzübersicht</target>
      </trans-unit>
      <trans-unit id="noRecordFound.infobox.title" resname="noRecordFound.infobox.title" approved="yes">
        <source>No TypoScript record on the current page</source>
        <target state="final">Kein TypoScript Datensatz auf der aktuellen Seite</target>
      </trans-unit>
      <trans-unit id="noRecordFound.infobox.message" resname="noRecordFound.infobox.message" approved="yes">
        <source>You need to create a TypoScript record in order to edit your configuration.</source>
        <target state="final">Sie müssen einen TypoScript Datensatz erstellen, um Ihre Konfiguration bearbeiten zu können.</target>
      </trans-unit>
      <trans-unit id="noRecordFound.goToClosestRecord.description" resname="noRecordFound.goToClosestRecord.description" approved="yes">
        <source>The closest TypoScript record is located on page '%s' (uid %s).</source>
        <target state="final">Der nächste TypoScript-Datensatz befindet sich auf Seite '%s' (UID %s).</target>
      </trans-unit>
      <trans-unit id="noRecordFound.goToClosestRecord.link.title" resname="noRecordFound.goToClosestRecord.link.title" approved="yes">
        <source>Select this TypoScript record</source>
        <target state="final">Diesen TypoScript Datensatz auswählen</target>
      </trans-unit>
      <trans-unit id="noRecordFound.createRootTypoScriptRecord.headline" resname="noRecordFound.createRootTypoScriptRecord.headline" approved="yes">
        <source>Root TypoScript record</source>
        <target state="final">Root TypoScript Datensatz</target>
      </trans-unit>
      <trans-unit id="noRecordFound.createRootTypoScriptRecord.description" resname="noRecordFound.createRootTypoScriptRecord.description" approved="yes">
        <source>Choose this option if you want this page to be the root of a new site.</source>
        <target state="final">Wählen Sie diese Option, wenn Sie möchten, dass diese Seite die Root-Seite einer neuen Site ist.</target>
      </trans-unit>
      <trans-unit id="noRecordFound.createRootTypoScriptRecord.link.title" resname="noRecordFound.createRootTypoScriptRecord.link.title" approved="yes">
        <source>Create a root TypoScript record</source>
        <target state="final">Neuen Root-TypoScript Datensatz erstellen</target>
      </trans-unit>
      <trans-unit id="noRecordFound.createRootTypoScriptRecord.title.placeholder" resname="noRecordFound.createRootTypoScriptRecord.title.placeholder" approved="yes">
        <source>NEW SITE</source>
        <target state="final">NEUE SITE</target>
      </trans-unit>
      <trans-unit id="noRecordFound.createAdditionalTypoScriptRecord.headline" resname="noRecordFound.createAdditionalTypoScriptRecord.headline" approved="yes">
        <source>Additional TypoScript record</source>
        <target state="final">Zusätzlicher TypoScript Datensatz</target>
      </trans-unit>
      <trans-unit id="noRecordFound.createAdditionalTypoScriptRecord.description" resname="noRecordFound.createAdditionalTypoScriptRecord.description" approved="yes">
        <source>An additional TypoScript record allows you to enter TypoScript values that will affect only this page and subpages.</source>
        <target state="final">Ein zusätzlicher TypoScript Datensatz erlaubt es, TypoScript-Werte einzugeben, die nur diese Seite und deren Unterseiten betreffen.</target>
      </trans-unit>
      <trans-unit id="noRecordFound.createAdditionalTypoScriptRecord.link.title" resname="noRecordFound.createAdditionalTypoScriptRecord.link.title" approved="yes">
        <source>Create an additional TypoScript record</source>
        <target state="final">Zusätzlichen TypoScript Datensatz erstellen</target>
      </trans-unit>
    </body>
  </file>
</xliff>
