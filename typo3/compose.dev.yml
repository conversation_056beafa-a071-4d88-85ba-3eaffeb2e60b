---
services:

  mariadb:
    image: mariadb:10.11
    ports:
      - "3408:3306"
    volumes:
      - mariadb-data:/var/lib/mysql
    networks:
      gdmcom_network:
        aliases:
          - mariadb
    env_file:
      - env-mariadb.ini
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--su-mysql", "--connect"]
      interval: 1s
      timeout: 5s
      retries: 90

  minio:
    image: minio/minio
    command: server /data
    ports:
      - "9068:9000"
    volumes:
      - ./data/minio:/data
    networks:
      gdmcom_network:
        aliases:
          - minio
    environment:
      - VIRTUAL_HOST=files.gdmcom.test
      - VIRTUAL_PORT=9068

  typo3:
    build: docker/typo3-dev/
    volumes:
      - ./typo3:/var/www/typo3/
    ports:
      - "5668:80"
    environment:
      - MINIO_PROD_GDMCOM_USER=typo3-readonly
      - MINIO_PROD_GDMCOM_PASSWORD=La6eiBahnuv4do2k
      - MINIO_PROD_GDMCOM_CUSTOM_HOST=https://files.gdmcom-gruppe.de
      - MINIO_PROD_GDMCOM_BUCKET_NAME=gdmcom-typo3
      - MINIO_PROD_GDMCOM_PUBLIC_URL=files.gdmcom-gruppe.de/gdmcom-typo3/
      - MINIO_PROD_GIBY_USER=typo3-readonly
      - MINIO_PROD_GIBY_PASSWORD=La6eiBahnuv4do2k
      - MINIO_PROD_GIBY_CUSTOM_HOST=https://files.giby-glasfaser.de
      - MINIO_PROD_GIBY_BUCKET_NAME=giby-typo3
      - MINIO_PROD_GIBY_PUBLIC_URL=files.giby-glasfaser.de/giby-typo3/
      - MINIO_LOCAL_GDMCOM_USER=typo3-dev
      - MINIO_LOCAL_GDMCOM_PASSWORD=Quai0Gei
      - MINIO_LOCAL_GDMCOM_CUSTOM_HOST=http://minio:9000
      - MINIO_LOCAL_GDMCOM_BUCKET_NAME=typo3
      - MINIO_LOCAL_GDMCOM_PUBLIC_URL=files.gdmcom.test:9068/typo3/
      - MINIO_LOCAL_GIBY_USER=typo3-dev
      - MINIO_LOCAL_GIBY_PASSWORD=Quai0Gei
      - MINIO_LOCAL_GIBY_CUSTOM_HOST=http://minio:9000
      - MINIO_LOCAL_GIBY_BUCKET_NAME=giby-typo3
      - MINIO_LOCAL_GIBY_PUBLIC_URL=files.gdmcom.test:9068/giby-typo3/
      - TYPO3_CONTEXT=Development/Local
      - SENTRY_RELEASE=develop
      #nginx-proxy configuration:
      - VIRTUAL_HOST=gdmcom.test
      - VIRTUAL_PORT=5668
    networks:
      gdmcom_network:
        aliases:
          - gdmcom.test
    depends_on:
      mariadb:
        condition: service_healthy
      minio:
        condition: service_started

  build:
    build: docker/build-frontend-assets
    volumes:
      - ./typo3:/var/www
    restart: "no"
    profiles:
      - tools

  sync-database:
    build: docker/sync-database
    volumes:
      - .:/data
      - ./docker/sync-database/syncDatabase.sh:/syncDatabase.sh
    env_file:
      - env-mariadb.ini
    environment:
      - BUCKET_NAME=gdmcom-dumps
      - DIRECTORY_PATH=gdmcom/main
    depends_on:
      mariadb:
        condition: service_healthy
    networks:
      gdmcom_network:
    restart: "no"
    profiles:
      - tools

volumes:
  mariadb-data:

networks:
  gdmcom_network:
    name: gdmcom
