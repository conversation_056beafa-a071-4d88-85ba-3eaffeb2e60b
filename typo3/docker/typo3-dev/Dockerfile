FROM gitlab-docker.mogic.com/docker/mogic-base-image:webapp-noble-php8.3

ADD conf /etc/mogic

RUN ln -sf /etc/mogic/php/90-gdmcom.ini /etc/php/8.3/fpm/conf.d/\
    && ln -sf /etc/mogic/php/90-gdmcom.ini /etc/php/8.3/cli/conf.d/\
    && ln -sf /etc/mogic/php/fpm-gdmcom.conf /etc/php/8.3/fpm/pool.d/gdmcom.conf\
    && ln -sf /etc/mogic/php/fpm-www.conf /etc/php/8.3/fpm/pool.d/www.conf\
    && ln -sf /etc/mogic/nginx/site-gdmcom /etc/nginx/sites-enabled/gdmcom\
    && rm /etc/nginx/sites-enabled/default\
    \
    && ln -sf /etc/mogic/nginx/nginx.conf /etc/nginx/\
    && ln -sf /etc/mogic/nginx/fastcgi_params-extra /etc/nginx/\
    && cp /etc/mogic/cron/* /etc/cron.d/\
    \
    && chown -R www-data:www-data /var/www

VOLUME ["/var/www/typo3"]
WORKDIR /var/www/typo3/
