# Default server
map $http_x_request_id $uuid {
    default   "${request_id}";
    ~*        "${http_x_request_id}";
}

log_format trace '$remote_addr - $remote_user [$time_local] "$request" '
                 '$status $body_bytes_sent "$http_referer" "$http_user_agent" '
                 '"$http_x_forwarded_for" $uuid';

server {
    listen       80 default_server;
    listen       5667;
    server_name  _;

    add_header X-Request-ID $uuid;

    client_body_buffer_size 10M;
    client_max_body_size    250M;

    root   /var/www/typo3/public;
    index  index.php index.html index.htm;

    error_log /dev/stdout warn;

    #do not spam graylog with 404s
    log_not_found off;

    set_real_ip_from *********/8;
    real_ip_header X-Forwarded-For;

    # FPM Statuspage
    location ~ ^/(fpm-statuspage|ping)$ {
        allow *************;
        access_log off;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_pass   unix:/run/php/php8.2-fpm.sock;
    }

    ### Example config
    ### https://docs.typo3.org/m/typo3/tutorial-getting-started/12.4/en-us/SystemRequirements/Index.html#web-server
    # Compressing resource files will save bandwidth and so improve loading speed especially for users
    # with slower internet connections. TYPO3 can compress the .js and .css files for you.
    # *) Set $GLOBALS['TYPO3_CONF_VARS']['BE']['compressionLevel'] = 9 for the Backend
    # *) Set $GLOBALS['TYPO3_CONF_VARS']['FE']['compressionLevel'] = 9 together with the TypoScript properties
    #    config.compressJs and config.compressCss for GZIP compression of Frontend JS and CSS files.
    location ~ \.js\.gzip$ {
        add_header Content-Encoding gzip;
        gzip off;
        types { text/javascript gzip; }
    }
    location ~ \.css\.gzip$ {
        add_header Content-Encoding gzip;
        gzip off;
        types { text/css gzip; }
    }

    # TYPO3 - Rule for versioned static files, configured through:
    # - $GLOBALS['TYPO3_CONF_VARS']['BE']['versionNumberInFilename']
    # - $GLOBALS['TYPO3_CONF_VARS']['FE']['versionNumberInFilename']
    if (!-e $request_filename) {
        rewrite ^/(.+)\.(\d+)\.(php|js|css|png|jpg|gif|gzip)$ /$1.$3 last;
    }

    # TYPO3 - Block access to composer files
    location ~* composer\.(?:json|lock) {
        deny all;
    }

    # TYPO3 - Block access to flexform files
    location ~* flexform[^.]*\.xml {
        deny all;
    }

    # TYPO3 - Block access to language files
    location ~* locallang[^.]*\.(?:xml|xlf)$ {
        deny all;
    }

    # TYPO3 - Block access to static typoscript files
    location ~* ext_conf_template\.txt|ext_typoscript_constants\.txt|ext_typoscript_setup\.txt {
        deny all;
    }

    # TYPO3 - Block access to miscellaneous protected files
    location ~* /.*\.(?:bak|co?nf|cfg|ya?ml|ts|typoscript|tsconfig|dist|fla|in[ci]|log|sh|sql|sqlite)$ {
        deny all;
    }

    # TYPO3 - Block access to recycler and temporary directories
    location ~ _(?:recycler|temp)_/ {
        deny all;
    }

    # TYPO3 - Block access to configuration files stored in fileadmin
    location ~ fileadmin/(?:templates)/.*\.(?:txt|ts|typoscript)$ {
        deny all;
    }

    # TYPO3 - Block access to libraries, source and temporary compiled data
    location ~ ^(?:vendor|typo3_src|typo3temp/var) {
        deny all;
    }

    # TYPO3 - Block access to protected extension directories
    location ~ (?:typo3conf/ext|typo3/sysext|typo3/ext)/[^/]+/(?:Configuration|Resources/Private|Tests?|Documentation|docs?)/ {
        deny all;
    }

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location = /typo3 {
        rewrite ^ /typo3/;
    }

    location /typo3/ {
        absolute_redirect off;
        try_files $uri /typo3/index.php$is_args$args;
    }

    location ~ [^/]\.php(/|$) {
        fastcgi_split_path_info ^(.+?\.php)(/.*)$;
        if (!-f $document_root$fastcgi_script_name) {
            return 404;
        }
        fastcgi_buffer_size 32k;
        fastcgi_buffers 8 16k;
        fastcgi_connect_timeout 240s;
        fastcgi_read_timeout 240s;
        fastcgi_send_timeout 240s;

        include /etc/nginx/fastcgi_params;
        fastcgi_pass unix:/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param HTTP_X_REQUEST_ID $uuid;
        include /etc/nginx/fastcgi_params-extra;
    }
}
