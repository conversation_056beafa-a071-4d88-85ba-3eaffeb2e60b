#!/bin/bash
# make environment variables available to cron
cronEnvVars="\
    ELANDERS_API_USER\
    ELANDERS_API_PASSWORD\
    FIO_WEBMAKLER_ID\
    FIO_WEBMAKLER_PASSWORD\
    FIO_WEBMAKLER_CLIENT_ID\
    FIO_WEBMAKLER_CLIENT_SECRET\
    MINIO_LOCAL_USER\
    MINIO_LOCAL_PASSWORD\
    MINIO_LOCAL_PUBLIC_URL\
    MINIO_PROD_USER\
    MINIO_PROD_PASSWORD\
    MINIO_PROD_PUBLIC_URL\
    MYSQL_HOST\
    MYSQL_DATABASE\
    MYSQL_USER\
    MYSQL_PASSWORD\
    TYPO3_CONTEXT\
    SENTRY_RELEASE\
    "

for varname in $cronEnvVars; do
    echo "$varname=${!varname}" >> /etc/environment
done
