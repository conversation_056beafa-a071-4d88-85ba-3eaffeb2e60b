FROM gitlab-docker.mogic.com/docker/mogic-base-image:webapp-noble-php8.3

ADD docker/typo3/conf /etc/mogic

RUN ln -sf /etc/mogic/php/90-gdmcom.ini /etc/php/8.3/fpm/conf.d/\
    && ln -sf /etc/mogic/php/90-gdmcom.ini /etc/php/8.3/cli/conf.d/\
    && ln -sf /etc/mogic/php/fpm-gdmcom.conf /etc/php/8.3/fpm/pool.d/gdmcom.conf\
    && ln -sf /etc/mogic/php/fpm-www.conf /etc/php/8.3/fpm/pool.d/www.conf\
    && ln -sf /etc/mogic/nginx/site-gdmcom /etc/nginx/sites-enabled/gdmcom\
    && rm /etc/nginx/sites-enabled/default\
    \
    && ln -sf /etc/mogic/nginx/nginx.conf /etc/nginx/\
    && ln -sf /etc/mogic/nginx/fastcgi_params-extra /etc/nginx/\
    && cp /etc/mogic/cron/* /etc/cron.d/\
    && cp /etc/mogic/setup.sh /root/setup.sh && chmod +x /root/setup.sh\
    \
    && rm /etc/supervisor/conf.d/cront.conf || true\
    && ln -s /etc/mogic/supervisor/conf.d/cront.conf /etc/supervisor/conf.d/cront.conf\
    \
    && chown -R www-data:www-data /var/www

COPY --chown=www-data:www-data ./typo3/ /var/www/typo3/

VOLUME ["/var/www/typo3"]
WORKDIR /var/www/typo3/
