GDMcom TYPO3-Website
********************


URLs
====

Testsystem

- Frontend: https://develop.gdmcom.mogic-server.de/
  - Basic-Auth: https://keepersecurity.eu/vault/#detail/4SZdOYwPGtOw_KDWjf8MzQ
- Backend: https://develop.gdmcom.mogic-server.de/typo3/
  - admin68gdm / https://keepersecurity.eu/vault/#detail/x8gBNvSigLG-MR7GD_vHuQ

Stagesystem

- Frontend: https://staging.gdmcom.mogic-server.de/
  - Basic-Auth: https://keepersecurity.eu/vault/#detail/JrkYZT8BqIuI92c8gRouRA
- Backend: https://staging.gdmcom.mogic-server.de/typo3/
  - admin68gdm / https://keepersecurity.eu/vault/#detail/x8gBNvSigLG-MR7GD_vHu<PERSON> Entwicklungssystem:

- Frontend:

  - http://gdmcom.test:5668/
  - http://giby.test:5668/
- Backend: http://gdmcom.test:5668/typo3/

  - Nutzer: admin68gdm
  - Passwort: https://keepersecurity.eu/vault/#detail/h_cokKufwXEeq9qYKTl2yA


Setup
=====
Einträge in die ``/etc/hosts``::

  127.0.0.1 gdmcom.test giby.test files.gdmcom.test
  ::1       gdmcom.test giby.test files.gdmcom.test

Initiales Setup::

  $ <NAME_EMAIL>:gdmcom/typo3.git
  $ make up-new-keep-sources

Hier wird man nach Zugangsdaten für S3 gefragt.
Diese sind zu finden unter https://service.mogic.com/user/profile/minio


Start
=====
Wenn man schonmal aufgesetzt hat::

  $ make start


Troubleshooting
+++++++++++++++

Fehler beim Aufsetzen über MacOS (mit M1 Chip)
##############################################

Wenn der Fehler beim Task ``make build-frontend-assets`` kommt::

  $ qemu-x86_64: Could not open '/lib/ld-musl-x86_64.so.1': No such file or directory

Solltet ihr die ``DOCKER_DEFAULT_PLATFORM=linux/amd64`` wählen::

  $ export DOCKER_DEFAULT_PLATFORM=linux/amd64

oder für den Standard in deiner Shell::

  $ echo "export DOCKER_DEFAULT_PLATFORM=linux/amd64" >> ~./zshrc #für zsh shells
