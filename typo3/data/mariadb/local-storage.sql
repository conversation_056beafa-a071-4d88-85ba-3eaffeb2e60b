UPDATE sys_file_storage
  SET
    name = 'Production MinIO GDMcom',
    is_default = 0,
    is_writable = 0,
    processingfolder = '13:_processed_'
 WHERE uid = 3;

UPDATE sys_file_storage
  SET
    name = 'Production MinIO GIBY',
    is_default = 0,
    is_writable = 0,
    is_online = 1,
    processingfolder = '14:_processed_'
 WHERE uid = 4;

DELETE FROM `sys_file_storage` WHERE uid=13;
INSERT INTO `sys_file_storage` (
  `uid`, `pid`, `tstamp`, `crdate`, `deleted`,
  `name`, `description`,
  `is_default`, `is_browsable`, `is_public`, `is_writable`, `is_online`,
  `auto_extract_metadata`, `processingfolder`,
  `driver`,
  `configuration`
) VALUES (
  13, 0, 1704720023, 1704720042, 0,
  'Local MinIO GDMcom', '',
  '1', '1', '1', '1', '1',
  '1', NULL,
  'AusDriverAmazonS3',
  '<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<T3FlexForms>
    <data>
        <sheet index="sDEF">
            <language index="lDEF">
                <field index="basePath">
                    <value index="vDEF"></value>
                </field>
                <field index="pathType">
                    <value index="vDEF">relative</value>
                </field>
                <field index="caseSensitive">
                    <value index="vDEF">1</value>
                </field>
                <field index="baseUri">
                    <value index="vDEF"></value>
                </field>
                <field index="bucket">
                    <value index="vDEF">dummy-bucket</value>
                </field>
                <field index="region">
                    <value index="vDEF">eu-central-1</value>
                </field>
                <field index="customHost">
                    <value index="vDEF">http://minio:9000</value>
                </field>
                <field index="pathStyleEndpoint">
                    <value index="vDEF">1</value>
                </field>
                <field index="key">
                    <value index="vDEF">dummy-user</value>
                </field>
                <field index="secretKey">
                    <value index="vDEF">dummy-password</value>
                </field>
                <field index="publicBaseUrl">
                    <value index="vDEF">dummy-url</value>
                </field>
                <field index="cacheHeaderDuration">
                    <value index="vDEF">0</value>
                </field>
                <field index="protocol">
                    <value index="vDEF">http://</value>
                </field>
                <field index="signature">
                    <value index="vDEF">0</value>
                </field>
                <field index="baseFolder">
                    <value index="vDEF"></value>
                </field>
            </language>
        </sheet>
    </data>
</T3FlexForms>'
);

DELETE FROM `sys_file_storage` WHERE uid=14;
INSERT INTO `sys_file_storage` (
  `uid`, `pid`, `tstamp`, `crdate`, `deleted`,
  `name`, `description`,
  `is_default`, `is_browsable`, `is_public`, `is_writable`, `is_online`,
  `auto_extract_metadata`, `processingfolder`,
  `driver`,
  `configuration`
) VALUES (
  14, 0, 1704720023, 1704720042, 0,
  'Local MinIO GIBY', '',
  '0', '1', '1', '1', '1',
  '1', NULL,
  'AusDriverAmazonS3',
  '<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<T3FlexForms>
    <data>
        <sheet index="sDEF">
            <language index="lDEF">
                <field index="basePath">
                    <value index="vDEF"></value>
                </field>
                <field index="pathType">
                    <value index="vDEF">relative</value>
                </field>
                <field index="caseSensitive">
                    <value index="vDEF">1</value>
                </field>
                <field index="baseUri">
                    <value index="vDEF"></value>
                </field>
                <field index="bucket">
                    <value index="vDEF">dummy-bucket</value>
                </field>
                <field index="region">
                    <value index="vDEF">eu-central-1</value>
                </field>
                <field index="customHost">
                    <value index="vDEF">http://minio:9000</value>
                </field>
                <field index="pathStyleEndpoint">
                    <value index="vDEF">1</value>
                </field>
                <field index="key">
                    <value index="vDEF">dummy-user</value>
                </field>
                <field index="secretKey">
                    <value index="vDEF">dummy-password</value>
                </field>
                <field index="publicBaseUrl">
                    <value index="vDEF">dummy-url</value>
                </field>
                <field index="cacheHeaderDuration">
                    <value index="vDEF">0</value>
                </field>
                <field index="protocol">
                    <value index="vDEF">http://</value>
                </field>
                <field index="signature">
                    <value index="vDEF">0</value>
                </field>
                <field index="baseFolder">
                    <value index="vDEF"></value>
                </field>
            </language>
        </sheet>
    </data>
</T3FlexForms>'
);

UPDATE sys_filemounts
  SET
    title = 'Production GDMcom Komplettzugriff'
 WHERE uid = 1;

UPDATE sys_filemounts
  SET
    title = 'Production GIBY Komplettzugriff'
 WHERE uid = 2;

INSERT INTO sys_filemounts (
  `uid`, `pid`, `tstamp`, `deleted`, `hidden`, `sorting`,
  `description`, `title`, `identifier`, `read_only`
) VALUES (
  13,	0,	1713858442,	0,	0,	128,
  '',	'Lokal GDMcom Komplettzugriff',	'13:/',	0
);

INSERT INTO sys_filemounts (
  `uid`, `pid`, `tstamp`, `deleted`, `hidden`, `sorting`,
  `description`, `title`, `identifier`, `read_only`
) VALUES (
  14,	0,	1713858442,	0,	0,	128,
  '',	'Lokal GIBY Komplettzugriff',	'14:/',	0
);

UPDATE be_groups
  SET file_mountpoints = "1,13"
  WHERE uid = 3 AND title = "Recht: GDMcom";

UPDATE be_groups
  SET file_mountpoints = "2,14"
  WHERE uid = 4 AND title = "Recht: GIBY";

DELETE FROM sys_file_processedfile
  WHERE original IN (SELECT uid FROM sys_file WHERE storage IN (3,4));
